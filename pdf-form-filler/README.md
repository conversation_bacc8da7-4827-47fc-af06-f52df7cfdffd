# Florida Purchase Agreement PDF Form Filler

A web-based application that allows users to fill out Florida Purchase Agreement forms through a user-friendly interface and download the completed PDF.

## Features

### 🎯 **Complete Form Coverage**
- **All 30+ input fields** mapped from the reverse mapping JSON
- **74 conditional checkboxes** organized into logical groups
- **Auto-generated fields** (Tax ID, financing calculations)
- **Required field validation** with clear field name references

### 🧠 **Smart Conditional Logic**
- **Payment Method Decision Tree**: Cash vs. Financing automatically sets appropriate checkboxes
- **Loan Type Logic**: Conventional, FHA, VA options with rate type selection
- **Title & Closing Logic**: Seller vs. Buyer designation scenarios
- **Auto-calculations**: Financing amount based on purchase price and deposits

### 🎨 **Enhanced User Experience**
- **Pre-fill Sample Data** button with realistic data and checkbox selections
- **Auto-updating Tax ID** field (combines County + Tax ID number)
- **Visual field mapping reference** showing all input names and PDF destinations
- **Conditional logic diagram** explaining IF statement scenarios
- **Responsive design** that works on desktop and mobile devices

### ⚡ **Advanced PDF Processing**
- **Real-time PDF generation** using the original PDF template
- **Intelligent checkbox mapping** based on user selections with reasoning
- **Multiple field population** (11 seller initials, 4 date fields, 16 additional terms)
- **Download functionality** for the completed PDF

## Files Included

- `index.html` - Frontend web interface with all input fields
- `server.js` - Backend Node.js server for PDF processing
- `package.json` - Dependencies and project configuration
- `Florida-As_Is-Purchase-Agreement.pdf` - Original blank PDF template
- `Florida-As_Is-Purchase-Agreement-copy-REVERSE-MAPPING.json` - Field mapping configuration

## Installation

1. **Install Node.js** (if not already installed)
   - Download from [nodejs.org](https://nodejs.org/)

2. **Install dependencies**
   ```bash
   cd pdf-form-filler
   npm install
   ```

## Usage

1. **Start the server**
   ```bash
   npm start
   ```
   
   Or for development with auto-restart:
   ```bash
   npm run dev
   ```

2. **Open your browser**
   - Navigate to `http://localhost:3000`

3. **Fill out the form**
   - Complete all required fields in the web interface
   - Optional fields can be left blank
   - Use the organized sections to find the information you need

4. **Generate PDF**
   - Click "Generate PDF" button
   - The completed PDF will automatically download

## Form Sections

The web interface is organized into the following sections:

- **Party Information** - Seller/buyer names and initials
- **Property Information** - Address, county, legal description
- **Personal Property** - Included/excluded items
- **Financial Information** - Purchase price, deposits, financing
- **Escrow Information** - Title company details
- **Address Information** - Buyer and seller addresses
- **Agent Information** - Real estate agent details
- **Additional Information** - Custom terms and dates

## How It Works

1. **User Input**: The web form collects all necessary information from the user
2. **Data Mapping**: The server uses the reverse mapping JSON to connect form fields to PDF fields
3. **PDF Processing**: The original PDF is loaded and filled with user data using pdf-lib
4. **Field Filling**: Text fields, checkboxes, and other form elements are populated
5. **Download**: The completed PDF is returned to the user for download

## Technical Details

- **Backend**: Node.js with Express framework
- **PDF Processing**: pdf-lib library for PDF manipulation
- **Frontend**: Vanilla HTML, CSS, and JavaScript
- **Mapping**: JSON-based field mapping system

## Customization

To modify the form or add new fields:

1. **Update the HTML form** in `index.html` to add new input fields
2. **Modify the mapping** in the JSON file to connect new fields to PDF fields
3. **Adjust the server logic** in `server.js` if needed for special processing

## Troubleshooting

- **PDF not generating**: Check that all required fields are filled
- **Server not starting**: Ensure Node.js is installed and dependencies are installed
- **Fields not filling**: Verify the field mapping in the JSON configuration file

## Dependencies

- `express` - Web server framework
- `pdf-lib` - PDF manipulation library
- `nodemon` - Development tool for auto-restart (dev dependency)

## License

MIT License - Feel free to modify and distribute as needed.