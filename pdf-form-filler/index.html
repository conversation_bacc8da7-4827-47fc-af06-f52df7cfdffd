<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Florida Purchase Agreement Form Filler</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .form-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-row.full-width {
            grid-template-columns: 1fr;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="date"],
        textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input:focus,
        textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        
        textarea {
            resize: vertical;
            height: 80px;
        }
        
        .submit-section {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
        }
        
        .submit-btn {
            background: #3498db;
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .submit-btn:hover {
            background: #2980b9;
        }
        
        .submit-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .prefill-btn {
            background: #27ae60;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-right: 15px;
        }
        
        .prefill-btn:hover {
            background: #229954;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            .header, .form-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Florida Purchase Agreement Form Filler</h1>
            <p>Fill out all the required information below to generate your completed purchase agreement PDF</p>
            
            <!-- Required Fields Summary -->
            <div style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: left;">
                <h3 style="color: #495057; margin-bottom: 15px; text-align: center;">📋 Required Field Names</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; font-size: 14px;">
                    <div>
                        <strong>🏠 Property Information:</strong>
                        <ul style="margin: 5px 0 0 20px; line-height: 1.4;">
                            <li><code>SELLER</code> - Seller name(s)</li>
                            <li><code>BUYER</code> - Buyer name(s)</li>
                            <li><code>[STREET ADDRESS, CITY, STATE ZIP]</code> - Property address</li>
                            <li><code>COUNTY</code> - County name</li>
                            <li><code>[LEGAL DESCRIPTION LINE 1]</code> - Legal description</li>
                        </ul>
                    </div>
                    <div>
                        <strong>💰 Financial Information:</strong>
                        <ul style="margin: 5px 0 0 20px; line-height: 1.4;">
                            <li><code>$[PURCHASE PRICE]</code> - Total purchase price</li>
                            <li><code>$[INITIAL DEPOSIT]</code> - Initial deposit amount</li>
                            <li><code>$[BALANCE TO CLOSE]</code> - Balance due at closing</li>
                            <li><code>[CLOSING DATE]</code> - Scheduled closing date</li>
                        </ul>
                    </div>
                    <div>
                        <strong>👥 Party Details:</strong>
                        <ul style="margin: 5px 0 0 20px; line-height: 1.4;">
                            <li><code>[SELLER INITIALS]</code> - Seller initials</li>
                            <li><code>[BUYER INITIALS]</code> - Buyer initials</li>
                            <li><code>[DATE]</code> - Contract date</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🏢 Escrow Information:</strong>
                        <ul style="margin: 5px 0 0 20px; line-height: 1.4;">
                            <li><code>[ESCROW AGENT/TITLE COMPANY NAME]</code> - Title company</li>
                            <li><code>[ESCROW AGENT ADDRESS]</code> - Title company address</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Conditional Logic Diagram -->
            <div style="background: #e3f2fd; border: 2px solid #bbdefb; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: left;">
                <h3 style="color: #1565c0; margin-bottom: 15px; text-align: center;">🔀 Conditional Logic Flow</h3>
                <div style="font-family: monospace; font-size: 13px; line-height: 1.6;">
                    <div style="background: white; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>Payment Method Decision Tree:</strong><br>
                        <pre style="margin: 10px 0; color: #2e7d32;">
┌─ User selects payment method ─┐
│                               │
├─ IF "Cash Purchase" ──────────┼─→ Check: "a Buyer will pay cash"
│                               │  └─→ Uncheck all financing options
│                               │
├─ IF "Financing Required" ─────┼─→ Check: "b This Contract is contingent"
│  │                            │  ├─→ IF "Conventional" → Check "conventional"
│  │                            │  ├─→ IF "FHA" → Check "FHA" 
│  │                            │  ├─→ IF "VA" → Check "VA"
│  │                            │  ├─→ IF "Fixed Rate" → Check "fixed"
│  │                            │  └─→ IF "Adjustable" → Check "adjustable"
│                               │
└─ Auto-calculate loan amount ──┴─→ $[FINANCING AMOUNT] = $[PURCHASE PRICE] - $[INITIAL DEPOSIT] - $[ADDITIONAL DEPOSIT]
                        </pre>
                    </div>
                    
                    <div style="background: white; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>Title & Closing Logic:</strong><br>
                        <pre style="margin: 10px 0; color: #c62828;">
┌─ User selects closing option ─┐
│                               │
├─ IF "Seller designates" ──────┼─→ Check: "i Seller shall designate Closing Agent"
│                               │  └─→ Auto-fill escrow info as seller responsibility
│                               │
└─ IF "Buyer designates" ───────┼─→ Check: "ii Buyer shall designate Closing Agent"
                                │  └─→ Auto-fill escrow info as buyer responsibility
                        </pre>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 5px;">
                        <strong>Auto-Generated Fields:</strong><br>
                        <pre style="margin: 10px 0; color: #f57c00;">
┌─ User enters data ────────────┐
│                               │
├─ County + Tax ID ─────────────┼─→ Auto-generate: "[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]"
│                               │
├─ Multiple Seller Initials ────┼─→ Populate all 11 seller initial fields in PDF
│                               │
├─ Multiple Buyer Initials ─────┼─→ Populate all 2 buyer initial fields in PDF
│                               │
├─ Multiple Date Fields ────────┼─→ Populate all 4 date fields in PDF
│                               │
└─ Additional Terms ────────────┼─→ Split into 16 separate PDF fields (20 ADDITIONAL TERMS 1-16)
                        </pre>
                    </div>
                </div>
            </div>

            <!-- Complete Field Mapping Reference -->
            <details style="background: #fff3e0; border: 2px solid #ffcc02; border-radius: 8px; padding: 20px; margin-top: 20px;">
                <summary style="cursor: pointer; font-weight: 600; color: #e65100; font-size: 16px;">📖 Complete Field Mapping Reference (Click to expand)</summary>
                <div style="margin-top: 15px; font-size: 13px; line-height: 1.5;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
                        
                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;">
                            <h4 style="color: #2e7d32; margin-bottom: 10px;">🏠 Property & Party Fields</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>SELLER</code> → PDF: "THIS FORM HAS BEEN APPROVED BY..."</li>
                                <li><code>BUYER</code> → PDF: "PARTIES"</li>
                                <li><code>[STREET ADDRESS, CITY, STATE ZIP]</code> → PDF: "PROPERTY DESCRIPTION"</li>
                                <li><code>COUNTY</code> → PDF: "a Street address city zip"</li>
                                <li><code>[TAX ID NUMBER]</code> → User input only</li>
                                <li><code>[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]</code> → PDF: "County Florida Property Tax ID"</li>
                                <li><code>[LEGAL DESCRIPTION LINE 1]</code> → PDF: "c Real Property The legal description is 1"</li>
                                <li><code>[LEGAL DESCRIPTION LINE 2]</code> → PDF: "c Real Property The legal description is 2"</li>
                            </ul>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;">
                            <h4 style="color: #1565c0; margin-bottom: 10px;">💰 Financial Fields</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>$[PURCHASE PRICE]</code> → PDF: "Text79"</li>
                                <li><code>$[INITIAL DEPOSIT]</code> → PDF: "Text80"</li>
                                <li><code>$[ADDITIONAL DEPOSIT]</code> → PDF: "Text81"</li>
                                <li><code>$[FINANCING AMOUNT]</code> → PDF: "Text82"</li>
                                <li><code>$[LOAN AMOUNT]</code> → PDF: "c Financing Express as a dollar amount..."</li>
                                <li><code>$[OTHER AMOUNT]</code> → PDF: "Text83"</li>
                                <li><code>$[BALANCE TO CLOSE]</code> → PDF: "Text84"</li>
                                <li><code>[CLOSING DATE]</code> → PDF: "Closing Date at the time..."</li>
                            </ul>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #ff9800;">
                            <h4 style="color: #ef6c00; margin-bottom: 10px;">🏢 Business & Agent Fields</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>[ESCROW AGENT/TITLE COMPANY NAME]</code> → PDF: "Escrow Agent Information Name"</li>
                                <li><code>[ESCROW AGENT ADDRESS]</code> → PDF: "Address"</li>
                                <li><code>[ESCROW AGENT EMAIL]</code> → PDF: "Email"</li>
                                <li><code>[ESCROW AGENT FAX]</code> → PDF: "Fax"</li>
                                <li><code>[BUYER'S AGENT NAME]</code> → PDF: "Cooperating Sales Associate if any"</li>
                                <li><code>[LISTING AGENT NAME]</code> → PDF: "Listing Sales Associate"</li>
                                <li><code>[BUYER'S BROKER COMPANY]</code> → PDF: "Cooperating Broker if any"</li>
                                <li><code>[LISTING BROKER COMPANY]</code> → PDF: "Listing Broker"</li>
                            </ul>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #e91e63;">
                            <h4 style="color: #c2185b; margin-bottom: 10px;">📍 Address & Contact Fields</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>[BUYER STREET ADDRESS]</code> → PDF: "Buyers address for purposes of notice 1"</li>
                                <li><code>[BUYER CITY, STATE ZIP]</code> → PDF: "Buyers address for purposes of notice 2"</li>
                                <li><code>[BUYER ADDRESS LINE 3]</code> → PDF: "Buyers address for purposes of notice 3"</li>
                                <li><code>[SELLER STREET ADDRESS]</code> → PDF: "Sellers address for purposes of notice 1"</li>
                                <li><code>[SELLER CITY, STATE ZIP]</code> → PDF: "Sellers address for purposes of notice 2"</li>
                                <li><code>[SELLER ADDRESS LINE 3]</code> → PDF: "Sellers address for purposes of notice 3"</li>
                            </ul>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #9c27b0;">
                            <h4 style="color: #7b1fa2; margin-bottom: 10px;">📝 Initials & Signatures</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>[SELLER INITIALS]</code> → PDF: 11 fields (Sellers Initials, Sellers Initials_2, etc.)</li>
                                <li><code>[BUYER INITIALS]</code> → PDF: 2 fields (Buyers Initials, Buyers Initials_2)</li>
                                <li><code>[DATE]</code> → PDF: 4 fields (Date, Date_2, Date_3, Date_4)</li>
                            </ul>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #607d8b;">
                            <h4 style="color: #455a64; margin-bottom: 10px;">📦 Property & Terms Fields</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><code>[PERSONAL PROPERTY INCLUDED]</code> → PDF: "and other access devices and storm..."</li>
                                <li><code>[ADDITIONAL PERSONAL PROPERTY LIST]</code> → PDF: "Other Personal Property items..."</li>
                                <li><code>[ITEMS EXCLUDED FROM PURCHASE]</code> → PDF: "e The following items are excluded..."</li>
                                <li><code>[OPTION II SHALL BE DEEMED SELECTED]</code> → PDF: "OPTION ii SHALL BE DEEMED SELECTED"</li>
                                <li><code>[ADDITIONAL TERMS]</code> → PDF: 16 fields (20 ADDITIONAL TERMS 1-16)</li>
                            </ul>
                        </div>

                    </div>

                    <div style="background: #f3e5f5; padding: 15px; border-radius: 5px; margin-top: 20px;">
                        <h4 style="color: #6a1b9a; margin-bottom: 10px;">☑️ Checkbox Logic Summary</h4>
                        <p style="margin: 0; line-height: 1.6;">
                            <strong>Total Checkboxes:</strong> 74 individual checkboxes in PDF<br>
                            <strong>User Selections:</strong> Organized into logical groups (Payment Method, Loan Type, Disclosures, etc.)<br>
                            <strong>Conditional Logic:</strong> User selections automatically trigger appropriate PDF checkboxes<br>
                            <strong>Examples:</strong> Selecting "Cash Purchase" automatically checks "a Buyer will pay cash" and unchecks all financing options
                        </p>
                    </div>
                </div>
            </details>
        </div>
        
        <form id="purchaseForm" class="form-container">
            <!-- Party Information -->
            <div class="form-section">
                <h2 class="section-title">Party Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="seller">Seller Name(s)</label>
                        <input type="text" id="seller" name="SELLER" placeholder="e.g., John Smith and Jane Smith" required>
                    </div>
                    <div class="form-group">
                        <label for="buyer">Buyer Name(s)</label>
                        <input type="text" id="buyer" name="BUYER" placeholder="e.g., Michael Johnson and Sarah Johnson" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sellerInitials">Seller Initials</label>
                        <input type="text" id="sellerInitials" name="[SELLER INITIALS]" placeholder="e.g., JS/JS" required>
                    </div>
                    <div class="form-group">
                        <label for="buyerInitials">Buyer Initials</label>
                        <input type="text" id="buyerInitials" name="[BUYER INITIALS]" placeholder="e.g., MJ/SJ" required>
                    </div>
                </div>
            </div>

            <!-- Property Information -->
            <div class="form-section">
                <h2 class="section-title">Property Information</h2>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="propertyAddress">Property Address</label>
                        <input type="text" id="propertyAddress" name="[STREET ADDRESS, CITY, STATE ZIP]" placeholder="e.g., 123 Main Street, Orlando, FL 32801" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="county">County</label>
                        <input type="text" id="county" name="COUNTY" placeholder="e.g., Orange" required>
                    </div>
                    <div class="form-group">
                        <label for="taxId">Property Tax ID Number</label>
                        <input type="text" id="taxId" name="[TAX ID NUMBER]" placeholder="e.g., 12-34-56-***********">
                    </div>
                </div>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="fullTaxId">Full Tax ID Description</label>
                        <input type="text" id="fullTaxId" name="[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]" placeholder="This will be auto-filled from County + Tax ID fields above">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="legalDesc1">Legal Description Line 1</label>
                        <input type="text" id="legalDesc1" name="[LEGAL DESCRIPTION LINE 1]" placeholder="e.g., Lot 15, Block A, Sunrise Subdivision" required>
                    </div>
                    <div class="form-group">
                        <label for="legalDesc2">Legal Description Line 2</label>
                        <input type="text" id="legalDesc2" name="[LEGAL DESCRIPTION LINE 2]" placeholder="Additional legal description (if needed)">
                    </div>
                </div>
            </div>

            <!-- Personal Property -->
            <div class="form-section">
                <h2 class="section-title">Personal Property</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="personalProperty">Personal Property Included</label>
                        <textarea id="personalProperty" name="[PERSONAL PROPERTY INCLUDED]" placeholder="List any personal property included in the sale"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="additionalProperty">Additional Personal Property</label>
                        <textarea id="additionalProperty" name="[ADDITIONAL PERSONAL PROPERTY LIST]" placeholder="Additional personal property details"></textarea>
                    </div>
                </div>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="excludedItems">Items Excluded from Purchase</label>
                        <textarea id="excludedItems" name="[ITEMS EXCLUDED FROM PURCHASE]" placeholder="e.g., Family portraits, personal artwork, shed in backyard"></textarea>
                    </div>
                </div>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="optionII">Option II Selection Text</label>
                        <input type="text" id="optionII" name="[OPTION II SHALL BE DEEMED SELECTED]" placeholder="Leave blank unless specific option text needed">
                    </div>
                </div>
            </div>

            <!-- Contract Options & Checkboxes -->
            <div class="form-section">
                <h2 class="section-title">Contract Options & Financing</h2>
                
                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Payment Method (Check one):</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="cash" style="margin-right: 8px;">
                            Cash Purchase (No Financing)
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="financing" style="margin-right: 8px;">
                            Financing Required
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Loan Type (If Financing):</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="conventional" style="margin-right: 8px;">
                            Conventional
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="fha" style="margin-right: 8px;">
                            FHA
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="va" style="margin-right: 8px;">
                            VA
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="other" style="margin-right: 8px;">
                            Other
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Interest Rate Type (If Financing):</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="fixed" style="margin-right: 8px;">
                            Fixed Rate
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="adjustable" style="margin-right: 8px;">
                            Adjustable Rate
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Title & Closing:</label>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="seller_designates_closing" style="margin-right: 8px;">
                            Seller shall designate Closing Agent and pay for Owner's Policy
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="buyer_designates_closing" style="margin-right: 8px;">
                            Buyer shall designate Closing Agent and pay for Owner's Policy
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Home Warranty:</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="buyer_warranty" style="margin-right: 8px;">
                            Buyer Pays
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="seller_warranty" style="margin-right: 8px;">
                            Seller Pays
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="na_warranty" style="margin-right: 8px;">
                            N/A
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Special Disclosures:</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="lead_paint" style="margin-right: 8px;">
                            Lead Paint Disclosure (Pre-1978)
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="property_subject_to_leases" style="margin-right: 8px;">
                            Property Subject to Leases
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="coastal_construction" style="margin-right: 8px;">
                            Coastal Construction Control
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="housing_older_persons" style="margin-right: 8px;">
                            Housing for Older Persons
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; margin-bottom: 15px;">Additional Riders/Addenda:</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="condominium_rider" style="margin-right: 8px;">
                            Condominium Rider
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="homeowners_assn" style="margin-right: 8px;">
                            Homeowners Association
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="seller_financing" style="margin-right: 8px;">
                            Seller Financing
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="short_sale" style="margin-right: 8px;">
                            Short Sale
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="appraisal_contingency" style="margin-right: 8px;">
                            Appraisal Contingency
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" name="Check if applicable" value="backup_contract" style="margin-right: 8px;">
                            Backup Contract
                        </label>
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="form-section">
                <h2 class="section-title">Financial Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchasePrice">Purchase Price</label>
                        <input type="text" id="purchasePrice" name="$[PURCHASE PRICE]" placeholder="e.g., $400,000" required>
                    </div>
                    <div class="form-group">
                        <label for="initialDeposit">Initial Deposit</label>
                        <input type="text" id="initialDeposit" name="$[INITIAL DEPOSIT]" placeholder="e.g., $10,000" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="additionalDeposit">Additional Deposit</label>
                        <input type="text" id="additionalDeposit" name="$[ADDITIONAL DEPOSIT]" placeholder="e.g., $15,000">
                    </div>
                    <div class="form-group">
                        <label for="financingAmount">Financing Amount</label>
                        <input type="text" id="financingAmount" name="$[FINANCING AMOUNT]" placeholder="e.g., $320,000">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="loanAmount">Loan Amount</label>
                        <input type="text" id="loanAmount" name="$[LOAN AMOUNT]" placeholder="e.g., $320,000">
                    </div>
                    <div class="form-group">
                        <label for="otherAmount">Other Amount</label>
                        <input type="text" id="otherAmount" name="$[OTHER AMOUNT]" placeholder="e.g., $0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="balanceToClose">Balance to Close</label>
                        <input type="text" id="balanceToClose" name="$[BALANCE TO CLOSE]" placeholder="e.g., $55,000" required>
                    </div>
                    <div class="form-group">
                        <label for="closingDate">Closing Date</label>
                        <input type="date" id="closingDate" name="[CLOSING DATE]" required>
                    </div>
                </div>
            </div>

            <!-- Escrow Information -->
            <div class="form-section">
                <h2 class="section-title">Escrow Agent / Title Company</h2>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="escrowName">Escrow Agent/Title Company Name</label>
                        <input type="text" id="escrowName" name="[ESCROW AGENT/TITLE COMPANY NAME]" placeholder="e.g., Sunshine Title Company LLC" required>
                    </div>
                </div>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="escrowAddress">Escrow Agent Address</label>
                        <input type="text" id="escrowAddress" name="[ESCROW AGENT ADDRESS]" placeholder="e.g., 456 Professional Blvd, Orlando, FL 32803" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="escrowEmail">Escrow Agent Email</label>
                        <input type="email" id="escrowEmail" name="[ESCROW AGENT EMAIL]" placeholder="e.g., <EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="escrowFax">Escrow Agent Fax</label>
                        <input type="tel" id="escrowFax" name="[ESCROW AGENT FAX]" placeholder="e.g., (*************">
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="form-section">
                <h2 class="section-title">Address Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="buyerStreet">Buyer Street Address</label>
                        <input type="text" id="buyerStreet" name="[BUYER STREET ADDRESS]" placeholder="e.g., 789 Oak Avenue">
                    </div>
                    <div class="form-group">
                        <label for="buyerCity">Buyer City, State, ZIP</label>
                        <input type="text" id="buyerCity" name="[BUYER CITY, STATE ZIP]" placeholder="e.g., Winter Park, FL 32789">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="buyerAddress3">Buyer Address Line 3</label>
                        <input type="text" id="buyerAddress3" name="[BUYER ADDRESS LINE 3]" placeholder="Additional address info (optional)">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sellerStreet">Seller Street Address</label>
                        <input type="text" id="sellerStreet" name="[SELLER STREET ADDRESS]" placeholder="e.g., 123 Main Street">
                    </div>
                    <div class="form-group">
                        <label for="sellerCity">Seller City, State, ZIP</label>
                        <input type="text" id="sellerCity" name="[SELLER CITY, STATE ZIP]" placeholder="e.g., Orlando, FL 32801">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sellerAddress3">Seller Address Line 3</label>
                        <input type="text" id="sellerAddress3" name="[SELLER ADDRESS LINE 3]" placeholder="Additional address info (optional)">
                    </div>
                </div>
            </div>

            <!-- Agent Information -->
            <div class="form-section">
                <h2 class="section-title">Agent Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="buyerAgent">Buyer's Agent Name</label>
                        <input type="text" id="buyerAgent" name="[BUYER'S AGENT NAME]" placeholder="e.g., Robert Martinez">
                    </div>
                    <div class="form-group">
                        <label for="listingAgent">Listing Agent Name</label>
                        <input type="text" id="listingAgent" name="[LISTING AGENT NAME]" placeholder="e.g., Lisa Thompson">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="buyerBroker">Buyer's Broker Company</label>
                        <input type="text" id="buyerBroker" name="[BUYER'S BROKER COMPANY]" placeholder="e.g., Premier Realty Group">
                    </div>
                    <div class="form-group">
                        <label for="listingBroker">Listing Broker Company</label>
                        <input type="text" id="listingBroker" name="[LISTING BROKER COMPANY]" placeholder="e.g., Elite Properties Inc">
                    </div>
                </div>
            </div>

            <!-- Additional Terms -->
            <div class="form-section">
                <h2 class="section-title">Additional Information</h2>
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="additionalTerms">Additional Terms</label>
                        <textarea id="additionalTerms" name="[ADDITIONAL TERMS]" placeholder="e.g., Property to be sold in as-is condition. Buyer has completed inspection." style="height: 120px;"></textarea>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="date">Date</label>
                        <input type="date" id="date" name="[DATE]" required>
                    </div>
                </div>
            </div>

            <div class="submit-section">
                <button type="button" class="prefill-btn" onclick="prefillForm()">Fill with Sample Data</button>
                <button type="submit" class="submit-btn">Generate PDF</button>
                <div id="status" class="status"></div>
            </div>
        </form>
    </div>

    <script>
        // Sample data for prefilling the form
        const sampleData = {
            "SELLER": "John Smith and Jane Smith",
            "BUYER": "Michael Johnson and Sarah Johnson",
            "[STREET ADDRESS, CITY, STATE ZIP]": "123 Main Street, Orlando, FL 32801",
            "COUNTY": "Orange",
            "[TAX ID NUMBER]": "12-34-56-***********",
            "[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]": "Orange County, Florida Property Tax ID: 12-34-56-***********",
            "[LEGAL DESCRIPTION LINE 1]": "Lot 15, Block A, Sunrise Subdivision",
            "[LEGAL DESCRIPTION LINE 2]": "According to the plat thereof recorded in Plat Book 45, Page 12",
            "[PERSONAL PROPERTY INCLUDED]": "All built-in appliances, ceiling fans, light fixtures, window treatments",
            "[ADDITIONAL PERSONAL PROPERTY LIST]": "Pool equipment, lawn mower, patio furniture",
            "[ITEMS EXCLUDED FROM PURCHASE]": "Family portraits, personal artwork, shed in backyard",
            "[ESCROW AGENT/TITLE COMPANY NAME]": "Sunshine Title Company LLC",
            "[ESCROW AGENT ADDRESS]": "456 Professional Blvd, Orlando, FL 32803",
            "[ESCROW AGENT EMAIL]": "<EMAIL>",
            "[ESCROW AGENT FAX]": "(*************",
            "$[PURCHASE PRICE]": "$400,000",
            "$[INITIAL DEPOSIT]": "$10,000",
            "$[ADDITIONAL DEPOSIT]": "$15,000",
            "$[FINANCING AMOUNT]": "$320,000",
            "$[LOAN AMOUNT]": "$320,000",
            "$[OTHER AMOUNT]": "$0",
            "$[BALANCE TO CLOSE]": "$55,000",
            "[SELLER INITIALS]": "JS/JS",
            "[BUYER INITIALS]": "MJ/SJ",
            "[BUYER STREET ADDRESS]": "789 Oak Avenue",
            "[BUYER CITY, STATE ZIP]": "Winter Park, FL 32789",
            "[BUYER ADDRESS LINE 3]": "",
            "[SELLER STREET ADDRESS]": "123 Main Street",
            "[SELLER CITY, STATE ZIP]": "Orlando, FL 32801",
            "[SELLER ADDRESS LINE 3]": "",
            "[BUYER'S AGENT NAME]": "Robert Martinez",
            "[LISTING AGENT NAME]": "Lisa Thompson",
            "[BUYER'S BROKER COMPANY]": "Premier Realty Group",
            "[LISTING BROKER COMPANY]": "Elite Properties Inc",
            "[ADDITIONAL TERMS]": "Property to be sold in as-is condition. Buyer has completed inspection and accepts property in current condition."
        };

        // Function to prefill the form with sample data
        function prefillForm() {
            const form = document.getElementById('purchaseForm');
            
            // Set text inputs, textareas, and other form fields
            Object.keys(sampleData).forEach(key => {
                const element = form.querySelector(`[name="${key}"]`);
                if (element) {
                    element.value = sampleData[key];
                }
            });
            
            // Set some common checkboxes
            const commonCheckboxes = [
                'financing',
                'conventional', 
                'fixed',
                'seller_designates_closing',
                'lead_paint'
            ];
            
            commonCheckboxes.forEach(value => {
                const checkbox = form.querySelector(`input[name="Check if applicable"][value="${value}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            // Set dates
            const today = new Date();
            const closingDate = new Date(Date.now() + 45 * 24 * 60 * 60 * 1000); // 45 days from now
            
            document.getElementById('date').valueAsDate = today;
            document.getElementById('closingDate').valueAsDate = closingDate;
            
            // Auto-update the full Tax ID field when county or tax ID changes
            updateFullTaxId();
            
            // Show success message
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status success';
            statusDiv.textContent = 'Form prefilled with sample data! You can now modify any fields as needed.';
            statusDiv.style.display = 'block';
            
            // Hide status after 3 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // Function to auto-update the full Tax ID field
        function updateFullTaxId() {
            const county = document.getElementById('county').value;
            const taxId = document.getElementById('taxId').value;
            const fullTaxIdField = document.getElementById('fullTaxId');
            
            if (county && taxId) {
                fullTaxIdField.value = `${county} County, Florida Property Tax ID: ${taxId}`;
            } else if (county) {
                fullTaxIdField.value = `${county} County, Florida Property Tax ID: `;
            } else {
                fullTaxIdField.value = '';
            }
        }

        // Add event listeners for auto-updating Tax ID field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('county').addEventListener('input', updateFullTaxId);
            document.getElementById('taxId').addEventListener('input', updateFullTaxId);
        });

        document.getElementById('purchaseForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.querySelector('.submit-btn');
            const statusDiv = document.getElementById('status');
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = 'Generating PDF...';
            statusDiv.style.display = 'none';
            
            try {
                // Collect form data
                const formData = new FormData(this);
                const data = {};
                
                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }
                
                // Send to backend
                const response = await fetch('/generate-pdf', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    // Create download link
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'Florida-Purchase-Agreement-Filled.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    // Show success message
                    statusDiv.className = 'status success';
                    statusDiv.textContent = 'PDF generated successfully! Download should start automatically.';
                    statusDiv.style.display = 'block';
                } else {
                    throw new Error('Failed to generate PDF');
                }
                
            } catch (error) {
                console.error('Error:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = 'Error generating PDF. Please try again.';
                statusDiv.style.display = 'block';
            } finally {
                // Reset button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Generate PDF';
            }
        });
        
        // Set today's date as default
        document.getElementById('date').valueAsDate = new Date();
        document.getElementById('closingDate').valueAsDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    </script>
</body>
</html>