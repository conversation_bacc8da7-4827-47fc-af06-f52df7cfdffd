const fs = require('fs');
const path = require('path');
const { PDFDocument } = require('pdf-lib');

async function analyzeMissingFields() {
    try {
        console.log('🔍 Analyzing PDF form fields vs JSON mapping...\n');
        
        // Load the PDF
        const pdfPath = path.join(__dirname, 'Florida-As_Is-Purchase-Agreement.pdf');
        const existingPdfBytes = fs.readFileSync(pdfPath);
        const pdfDoc = await PDFDocument.load(existingPdfBytes);
        
        // Get all form fields from PDF
        const form = pdfDoc.getForm();
        const fields = form.getFields();
        
        console.log(`📝 Found ${fields.length} total fields in PDF\n`);
        
        // Create a map of all PDF fields with their details
        const pdfFields = {};
        fields.forEach((field, index) => {
            const fieldName = field.getName();
            const fieldType = field.constructor.name;
            pdfFields[fieldName] = {
                fieldName: fieldName,
                fieldType: fieldType,
                index: index,
                suggestedMapping: "Unknown"
            };
        });
        
        // Load the existing JSON mapping
        const reverseMappingPath = path.join(__dirname, 'Florida-As_Is-Purchase-Agreement-copy-REVERSE-MAPPING.json');
        const reverseMappingData = fs.readFileSync(reverseMappingPath, 'utf8');
        const reverseMapping = JSON.parse(reverseMappingData);
        
        console.log(`📖 Loaded reverse mapping with ${Object.keys(reverseMapping).length} placeholder keys\n`);
        
        // Create a set of all field names that are mapped in the JSON
        const mappedFieldNames = new Set();
        
        Object.values(reverseMapping).forEach(mappingValue => {
            if (Array.isArray(mappingValue)) {
                // Handle arrays of field mappings (like SELLER INITIALS, BUYER INITIALS, etc.)
                mappingValue.forEach(item => {
                    mappedFieldNames.add(item.fieldName);
                });
            } else if (mappingValue.fieldName) {
                // Handle single field mappings
                mappedFieldNames.add(mappingValue.fieldName);
            }
        });
        
        console.log(`🗺️  Total unique field names in JSON mapping: ${mappedFieldNames.size}\n`);
        
        // Find fields in PDF that are NOT in the JSON mapping
        const missingFields = [];
        Object.keys(pdfFields).forEach(fieldName => {
            if (!mappedFieldNames.has(fieldName)) {
                missingFields.push(pdfFields[fieldName]);
            }
        });
        
        // Sort missing fields by index for better readability
        missingFields.sort((a, b) => a.index - b.index);
        
        console.log(`❌ MISSING FIELDS: ${missingFields.length} fields found in PDF but NOT in JSON mapping:\n`);
        
        if (missingFields.length > 0) {
            missingFields.forEach((field, i) => {
                console.log(`${i + 1}. Field Name: "${field.fieldName}"`);
                console.log(`   Field Type: ${field.fieldType}`);
                console.log(`   PDF Index: ${field.index}`);
                console.log('');
            });
            
            // Generate JSON structure for missing fields
            console.log('\n📋 JSON structure for missing fields:\n');
            console.log('Add these to your reverse mapping JSON:\n');
            
            missingFields.forEach(field => {
                const placeholder = `[${field.fieldName.toUpperCase().replace(/[^A-Z0-9]/g, '_')}]`;
                console.log(`"${placeholder}": {`);
                console.log(`  "fieldName": "${field.fieldName}",`);
                console.log(`  "fieldType": "${field.fieldType}",`);
                console.log(`  "index": ${field.index},`);
                console.log(`  "suggestedMapping": "General information"`);
                console.log(`},`);
            });
        } else {
            console.log('✅ All PDF fields are mapped in the JSON file!');
        }
        
        // Also check for fields in JSON that don't exist in PDF (reverse check)
        const jsonFieldsNotInPdf = [];
        mappedFieldNames.forEach(fieldName => {
            if (!pdfFields[fieldName]) {
                jsonFieldsNotInPdf.push(fieldName);
            }
        });
        
        if (jsonFieldsNotInPdf.length > 0) {
            console.log(`\n⚠️  EXTRA MAPPINGS: ${jsonFieldsNotInPdf.length} field names in JSON that don't exist in PDF:\n`);
            jsonFieldsNotInPdf.forEach((fieldName, i) => {
                console.log(`${i + 1}. "${fieldName}"`);
            });
        }
        
        console.log('\n📊 SUMMARY:');
        console.log(`Total PDF fields: ${fields.length}`);
        console.log(`Total mapped fields: ${mappedFieldNames.size}`);
        console.log(`Missing from JSON: ${missingFields.length}`);
        console.log(`Extra in JSON: ${jsonFieldsNotInPdf.length}`);
        
    } catch (error) {
        console.error('❌ Error analyzing fields:', error);
    }
}

// Run the analysis
analyzeMissingFields();
