const express = require('express');
const { PDFDocument, rgb, StandardFonts } = require('pdf-lib');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// Serve the HTML page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// PDF generation endpoint
app.post('/generate-pdf', async (req, res) => {
    try {
        console.log('🔄 Generating PDF with user data...');
        
        // Get user data from request
        const userData = req.body;
        console.log('📝 Received form data:', Object.keys(userData).length, 'fields');
        
        // Handle checkbox data - convert arrays to boolean flags
        const checkboxValues = userData['Check if applicable'] || [];
        const checkboxFlags = {};
        if (Array.isArray(checkboxValues)) {
            checkboxValues.forEach(value => {
                checkboxFlags[value] = true;
            });
        } else if (checkboxValues) {
            checkboxFlags[checkboxValues] = true;
        }

        // Implement conditional logic scenarios
        console.log('🔀 Applying conditional logic...');
        
        // SCENARIO 1: Payment Method Decision Tree
        if (checkboxFlags.cash) {
            console.log('💰 Cash purchase detected - setting cash payment checkbox');
            checkboxFlags.cash_payment = true;
            // Uncheck financing options for cash deals
            checkboxFlags.financing = false;
            checkboxFlags.conventional = false;
            checkboxFlags.fha = false;
            checkboxFlags.va = false;
        } else if (checkboxFlags.financing) {
            console.log('🏦 Financing required - setting financing contingency checkbox');
            checkboxFlags.financing_contingency = true;
        }

        // SCENARIO 2: Auto-calculate financing amount
        const purchasePrice = parseFloat(userData['$[PURCHASE PRICE]']?.replace(/[$,]/g, '') || '0');
        const initialDeposit = parseFloat(userData['$[INITIAL DEPOSIT]']?.replace(/[$,]/g, '') || '0');
        const additionalDeposit = parseFloat(userData['$[ADDITIONAL DEPOSIT]']?.replace(/[$,]/g, '') || '0');
        
        if (purchasePrice > 0 && !userData['$[FINANCING AMOUNT]']) {
            const calculatedFinancing = purchasePrice - initialDeposit - additionalDeposit;
            userData['$[FINANCING AMOUNT]'] = `$${calculatedFinancing.toLocaleString()}`;
            userData['$[LOAN AMOUNT]'] = `$${calculatedFinancing.toLocaleString()}`;
            console.log(`🧮 Auto-calculated financing amount: $${calculatedFinancing.toLocaleString()}`);
        }

        // SCENARIO 3: Auto-generate combined Tax ID field
        if (userData['COUNTY'] && userData['[TAX ID NUMBER]']) {
            userData['[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]'] = 
                `${userData['COUNTY']} County, Florida Property Tax ID: ${userData['[TAX ID NUMBER]']}`;
            console.log('📋 Auto-generated full Tax ID field');
        }

        console.log(`✅ Checkbox flags set: ${Object.keys(checkboxFlags).filter(k => checkboxFlags[k]).join(', ')}`);
        console.log(`📊 Processing ${Object.keys(userData).length} form fields...`);
        
        // Read the original PDF
        const pdfPath = path.join(__dirname, 'Florida-As_Is-Purchase-Agreement.pdf');
        const existingPdfBytes = fs.readFileSync(pdfPath);
        const pdfDoc = await PDFDocument.load(existingPdfBytes);
        
        // Read the reverse mapping
        const reverseMappingPath = path.join(__dirname, 'Florida-As_Is-Purchase-Agreement-copy-REVERSE-MAPPING.json');
        const reverseMappingData = fs.readFileSync(reverseMappingPath, 'utf8');
        const reverseMapping = JSON.parse(reverseMappingData);
        
        console.log(`📖 Loaded reverse mapping with ${Object.keys(reverseMapping).length} placeholder keys`);
        
        // Get the form
        const form = pdfDoc.getForm();
        const fields = form.getFields();
        
        console.log(`📝 Found ${fields.length} fields in PDF`);
        console.log('🔄 Filling fields with user data...\n');
        
        // Fill fields using reverse mapping and user data
        let filledCount = 0;
        let errorCount = 0;
        let skippedCount = 0;
        
        fields.forEach((field, index) => {
            const fieldName = field.getName();
            const fieldType = field.constructor.name;
            
            // Find the placeholder for this field in reverse mapping
            let placeholderValue = null;
            let mappingInfo = null;
            
            // Search through reverse mapping to find this field
            for (const [placeholder, info] of Object.entries(reverseMapping)) {
                if (Array.isArray(info)) {
                    // Handle arrays (multiple fields with same placeholder)
                    const matchingEntry = info.find(entry => entry.fieldName === fieldName);
                    if (matchingEntry) {
                        placeholderValue = userData[placeholder] || '';
                        mappingInfo = matchingEntry;
                        break;
                    }
                } else {
                    // Handle single field entries
                    if (info.fieldName === fieldName) {
                        placeholderValue = userData[placeholder] || '';
                        mappingInfo = info;
                        break;
                    }
                }
            }
            
            if (!mappingInfo) {
                console.log(`⚠️  No mapping found for field: "${fieldName}"`);
                skippedCount++;
                return;
            }
            
            try {
                switch (fieldType) {
                    case 'PDFTextField':
                        const textField = form.getTextField(fieldName);
                        const textValue = placeholderValue || '';
                        if (textValue) {
                            textField.setText(textValue);
                            textField.setFontSize(8);
                            console.log(`✅ ${index + 1}. Text field "${fieldName}" → "${textValue}"`);
                            filledCount++;
                        } else {
                            console.log(`○ ${index + 1}. Text field "${fieldName}" → (empty)`);
                        }
                        break;
                        
                    case 'PDFCheckBox':
                        const checkBox = form.getCheckBox(fieldName);
                        let shouldCheck = false;
                        let reason = '';
                        
                        // CONDITIONAL LOGIC IMPLEMENTATION
                        
                        // Payment Method Scenarios
                        if (checkboxFlags.cash && fieldName.includes('Buyer will pay cash')) {
                            shouldCheck = true;
                            reason = 'Cash purchase selected';
                        }
                        if (checkboxFlags.financing && fieldName.includes('contingent upon Buyer obtaining')) {
                            shouldCheck = true;
                            reason = 'Financing required';
                        }
                        
                        // Loan Type Scenarios (only if financing selected)
                        if (checkboxFlags.financing || !checkboxFlags.cash) {
                            if (checkboxFlags.conventional && fieldName.includes('conventional')) {
                                shouldCheck = true;
                                reason = 'Conventional loan selected';
                            }
                            if (checkboxFlags.fha && fieldName.includes('FHA')) {
                                shouldCheck = true;
                                reason = 'FHA loan selected';
                            }
                            if (checkboxFlags.va && fieldName.includes('VA')) {
                                shouldCheck = true;
                                reason = 'VA loan selected';
                            }
                            if (checkboxFlags.fixed && fieldName.includes('fixed')) {
                                shouldCheck = true;
                                reason = 'Fixed rate selected';
                            }
                            if (checkboxFlags.adjustable && fieldName.includes('adjustable')) {
                                shouldCheck = true;
                                reason = 'Adjustable rate selected';
                            }
                        }
                        
                        // Title & Closing Scenarios
                        if (checkboxFlags.seller_designates_closing && fieldName.includes('Seller shall designate')) {
                            shouldCheck = true;
                            reason = 'Seller designates closing';
                        }
                        if (checkboxFlags.buyer_designates_closing && fieldName.includes('Buyer shall designate')) {
                            shouldCheck = true;
                            reason = 'Buyer designates closing';
                        }
                        
                        // Disclosure Scenarios
                        if (checkboxFlags.lead_paint && fieldName.includes('Lead Paint')) {
                            shouldCheck = true;
                            reason = 'Lead paint disclosure required';
                        }
                        if (checkboxFlags.property_subject_to_leases && fieldName.includes('SUBJECT TO LEASES')) {
                            shouldCheck = true;
                            reason = 'Property subject to leases';
                        }
                        if (checkboxFlags.coastal_construction && fieldName.includes('Coastal Construction')) {
                            shouldCheck = true;
                            reason = 'Coastal construction disclosure';
                        }
                        if (checkboxFlags.housing_older_persons && fieldName.includes('Housing for Older Persons')) {
                            shouldCheck = true;
                            reason = 'Housing for older persons';
                        }
                        
                        // Rider/Addenda Scenarios
                        if (checkboxFlags.condominium_rider && fieldName.includes('Condominium Rider')) {
                            shouldCheck = true;
                            reason = 'Condominium rider selected';
                        }
                        if (checkboxFlags.homeowners_assn && fieldName.includes('Homeowners Assn')) {
                            shouldCheck = true;
                            reason = 'HOA rider selected';
                        }
                        if (checkboxFlags.seller_financing && fieldName.includes('Seller Financing')) {
                            shouldCheck = true;
                            reason = 'Seller financing selected';
                        }
                        if (checkboxFlags.short_sale && fieldName.includes('Short Sale')) {
                            shouldCheck = true;
                            reason = 'Short sale selected';
                        }
                        if (checkboxFlags.appraisal_contingency && fieldName.includes('Appraisal Contingency')) {
                            shouldCheck = true;
                            reason = 'Appraisal contingency selected';
                        }
                        if (checkboxFlags.backup_contract && fieldName.includes('Backup Contract')) {
                            shouldCheck = true;
                            reason = 'Backup contract selected';
                        }
                        
                        // Default scenarios for common contract elements
                        if (!shouldCheck && (
                            fieldName.includes('counters') ||
                            fieldName.includes('blank then 3 days after Effective Date')
                        )) {
                            shouldCheck = true;
                            reason = 'Default contract provision';
                        }
                        
                        // Apply the checkbox logic
                        if (shouldCheck) {
                            checkBox.check();
                            console.log(`✅ ${index + 1}. Checkbox "${fieldName}" → ✓ (${reason})`);
                        } else {
                            console.log(`○ ${index + 1}. Checkbox "${fieldName}" → ○ (unchecked)`);
                        }
                        filledCount++;
                        break;
                        
                    case 'PDFDropdown':
                        const dropdown = form.getDropdown(fieldName);
                        const options = dropdown.getOptions();
                        if (options && options.length > 0) {
                            dropdown.select(options[0]);
                            console.log(`✅ ${index + 1}. Dropdown "${fieldName}" → "${options[0]}"`);
                            filledCount++;
                        }
                        break;
                        
                    case 'PDFRadioGroup':
                        const radioGroup = form.getRadioGroup(fieldName);
                        const radioOptions = radioGroup.getOptions();
                        if (radioOptions && radioOptions.length > 0) {
                            radioGroup.select(radioOptions[0]);
                            console.log(`✅ ${index + 1}. Radio "${fieldName}" → "${radioOptions[0]}"`);
                            filledCount++;
                        }
                        break;
                        
                    default:
                        console.log(`❓ ${index + 1}. Unknown field type "${fieldType}" for "${fieldName}"`);
                }
            } catch (error) {
                console.log(`❌ ${index + 1}. Error filling "${fieldName}": ${error.message}`);
                errorCount++;
            }
        });
        
        // Format dates if provided
        if (userData['[CLOSING DATE]']) {
            const closingDate = new Date(userData['[CLOSING DATE]']);
            userData['[CLOSING DATE]'] = closingDate.toLocaleDateString('en-US');
        }
        
        if (userData['[DATE]']) {
            const date = new Date(userData['[DATE]']);
            userData['[DATE]'] = date.toLocaleDateString('en-US');
        }
        
        // Save the filled PDF
        const filledPdfBytes = await pdfDoc.save();
        
        console.log('\n🎉 PDF generation completed!');
        console.log(`📊 Statistics:`);
        console.log(`   - Fields successfully filled: ${filledCount}`);
        console.log(`   - Fields skipped (no mapping): ${skippedCount}`);
        console.log(`   - Errors encountered: ${errorCount}`);
        console.log(`   - Total fields processed: ${fields.length}`);
        console.log(`   - User data entries: ${Object.keys(userData).length}`);
        
        // Send PDF as response
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename="Florida-Purchase-Agreement-Filled.pdf"');
        res.send(Buffer.from(filledPdfBytes));
        
    } catch (error) {
        console.error('❌ Error generating PDF:', error.message);
        console.error(error.stack);
        res.status(500).json({ 
            error: 'Failed to generate PDF', 
            details: error.message 
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 PDF Form Filler server running on http://localhost:${PORT}`);
    console.log(`📝 Open your browser and navigate to http://localhost:${PORT} to use the form`);
});