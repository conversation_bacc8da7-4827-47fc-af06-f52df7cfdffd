{"SELLER": {"fieldName": "THIS FORM HAS BEEN APPROVED BY THE FLORIDA REALTORS AND THE FLORIDA BAR", "fieldType": "TextField", "index": 1, "suggestedMapping": "Form header text"}, "BUYER": {"fieldName": "PARTIES", "fieldType": "TextField", "index": 2, "suggestedMapping": "General information"}, "[STREET ADDRESS, CITY, STATE ZIP]": {"fieldName": "PROPERTY DESCRIPTION", "fieldType": "TextField", "index": 3, "suggestedMapping": "Property information"}, "COUNTY": {"fieldName": "a Street address city zip", "fieldType": "TextField", "index": 4, "suggestedMapping": "Address information"}, "[COUNTY] County, Florida Property Tax ID: [TAX ID NUMBER]": {"fieldName": "County Florida Property Tax ID", "fieldType": "TextField", "index": 5, "suggestedMapping": "Property information"}, "[LEGAL DESCRIPTION LINE 1]": {"fieldName": "c Real Property The legal description is 1", "fieldType": "TextField", "index": 6, "suggestedMapping": "Property information"}, "[LEGAL DESCRIPTION LINE 2]": {"fieldName": "c Real Property The legal description is 2", "fieldType": "TextField", "index": 7, "suggestedMapping": "Property information"}, "[PERSONAL PROPERTY INCLUDED]": {"fieldName": "and other access devices and storm shutterspanels Personal Property", "fieldType": "TextField", "index": 8, "suggestedMapping": "Property information"}, "[ADDITIONAL PERSONAL PROPERTY LIST]": {"fieldName": "Other Personal Property items included in this purchase are", "fieldType": "TextField", "index": 9, "suggestedMapping": "Property information"}, "[ITEMS EXCLUDED FROM PURCHASE]": {"fieldName": "e The following items are excluded from the purchase", "fieldType": "TextField", "index": 11, "suggestedMapping": "General information"}, "[OPTION II SHALL BE DEEMED SELECTED]": {"fieldName": "OPTION ii SHALL BE DEEMED SELECTED", "fieldType": "TextField", "index": 14, "suggestedMapping": "General information"}, "[ESCROW AGENT/TITLE COMPANY NAME]": {"fieldName": "Escrow Agent Information Name", "fieldType": "TextField", "index": 15, "suggestedMapping": "Agent/Broker information"}, "[ESCROW AGENT ADDRESS]": {"fieldName": "Address", "fieldType": "TextField", "index": 16, "suggestedMapping": "Address information"}, "[ESCROW AGENT EMAIL]": {"fieldName": "Email", "fieldType": "TextField", "index": 17, "suggestedMapping": "General information"}, "[ESCROW AGENT FAX]": {"fieldName": "Fax", "fieldType": "TextField", "index": 18, "suggestedMapping": "General information"}, "$[LOAN AMOUNT]": {"fieldName": "c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8", "fieldType": "TextField", "index": 20, "suggestedMapping": "Financial information"}, "[CLOSING DATE]": {"fieldName": "Closing Date at the time established by the Closing Agent", "fieldType": "TextField", "index": 23, "suggestedMapping": "Date information"}, "[SELLER INITIALS]": [{"fieldName": "Sellers Initials", "fieldType": "TextField", "index": 24, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_2", "fieldType": "TextField", "index": 64, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_3", "fieldType": "TextField", "index": 76, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_4", "fieldType": "TextField", "index": 83, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_5", "fieldType": "TextField", "index": 88, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_6", "fieldType": "TextField", "index": 92, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_7", "fieldType": "TextField", "index": 96, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_8", "fieldType": "TextField", "index": 100, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_9", "fieldType": "TextField", "index": 104, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_10", "fieldType": "TextField", "index": 155, "suggestedMapping": "Seller information"}, {"fieldName": "Sellers Initials_11", "fieldType": "TextField", "index": 178, "suggestedMapping": "Seller information"}], "$[PURCHASE PRICE]": {"fieldName": "Text79", "fieldType": "TextField", "index": 25, "suggestedMapping": "General information"}, "$[INITIAL DEPOSIT]": {"fieldName": "Text80", "fieldType": "TextField", "index": 26, "suggestedMapping": "General information"}, "$[ADDITIONAL DEPOSIT]": {"fieldName": "Text81", "fieldType": "TextField", "index": 27, "suggestedMapping": "General information"}, "$[FINANCING AMOUNT]": {"fieldName": "Text82", "fieldType": "TextField", "index": 28, "suggestedMapping": "General information"}, "$[OTHER AMOUNT]": {"fieldName": "Text83", "fieldType": "TextField", "index": 29, "suggestedMapping": "General information"}, "$[BALANCE TO CLOSE]": {"fieldName": "Text84", "fieldType": "TextField", "index": 30, "suggestedMapping": "General information"}, "[BUYER INITIALS]": [{"fieldName": "Buyers Initials", "fieldType": "TextField", "index": 153, "suggestedMapping": "Buyer information"}, {"fieldName": "Buyers Initials_2", "fieldType": "TextField", "index": 175, "suggestedMapping": "Buyer information"}], "[DATE]": [{"fieldName": "Date", "fieldType": "TextField", "index": 162, "suggestedMapping": "Date information"}, {"fieldName": "Date_2", "fieldType": "TextField", "index": 163, "suggestedMapping": "Date information"}, {"fieldName": "Date_3", "fieldType": "TextField", "index": 164, "suggestedMapping": "Date information"}, {"fieldName": "Date_4", "fieldType": "TextField", "index": 165, "suggestedMapping": "Date information"}], "[BUYER STREET ADDRESS]": {"fieldName": "Buyers address for purposes of notice 1", "fieldType": "TextField", "index": 166, "suggestedMapping": "Buyer information"}, "[BUYER CITY, STATE ZIP]": {"fieldName": "Buyers address for purposes of notice 2", "fieldType": "TextField", "index": 167, "suggestedMapping": "Buyer information"}, "[BUYER ADDRESS LINE 3]": {"fieldName": "Buyers address for purposes of notice 3", "fieldType": "TextField", "index": 168, "suggestedMapping": "Buyer information"}, "[SELLER STREET ADDRESS]": {"fieldName": "Sellers address for purposes of notice 1", "fieldType": "TextField", "index": 169, "suggestedMapping": "Seller information"}, "[SELLER CITY, STATE ZIP]": {"fieldName": "Sellers address for purposes of notice 2", "fieldType": "TextField", "index": 170, "suggestedMapping": "Seller information"}, "[SELLER ADDRESS LINE 3]": {"fieldName": "Sellers address for purposes of notice 3", "fieldType": "TextField", "index": 171, "suggestedMapping": "Seller information"}, "[BUYER'S AGENT NAME]": {"fieldName": "Cooperating Sales Associate if any", "fieldType": "TextField", "index": 172, "suggestedMapping": "General information"}, "[LISTING AGENT NAME]": {"fieldName": "Listing Sales Associate", "fieldType": "TextField", "index": 173, "suggestedMapping": "General information"}, "[BUYER'S BROKER COMPANY]": {"fieldName": "Cooperating Broker if any", "fieldType": "TextField", "index": 174, "suggestedMapping": "Agent/Broker information"}, "[LISTING BROKER COMPANY]": {"fieldName": "Listing Broker", "fieldType": "TextField", "index": 177, "suggestedMapping": "Agent/Broker information"}, "[ADDITIONAL TERMS]": [{"fieldName": "20 ADDITIONAL TERMS 1", "fieldType": "TextField", "index": 135, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 2", "fieldType": "TextField", "index": 136, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 3", "fieldType": "TextField", "index": 137, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 4", "fieldType": "TextField", "index": 138, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 5", "fieldType": "TextField", "index": 139, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 6", "fieldType": "TextField", "index": 140, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 7", "fieldType": "TextField", "index": 141, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 8", "fieldType": "TextField", "index": 142, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 9", "fieldType": "TextField", "index": 143, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 10", "fieldType": "TextField", "index": 144, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 11", "fieldType": "TextField", "index": 145, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 12", "fieldType": "TextField", "index": 146, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 13", "fieldType": "TextField", "index": 147, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 14", "fieldType": "TextField", "index": 148, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 15", "fieldType": "TextField", "index": 149, "suggestedMapping": "Additional terms"}, {"fieldName": "20 ADDITIONAL TERMS 16", "fieldType": "TextField", "index": 150, "suggestedMapping": "Additional terms"}], "Check if applicable": [{"fieldName": "accompanies offer or ii", "fieldType": "CheckBox", "index": 12, "suggestedMapping": "General information"}, {"fieldName": "blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN", "fieldType": "CheckBox", "index": 13, "suggestedMapping": "Date information"}, {"fieldName": "CHECK IF PROPERTY IS SUBJECT TO LEASES OR OCCUPANCY AFTER CLOSING If Property is", "fieldType": "CheckBox", "index": 34, "suggestedMapping": "Property information"}, {"fieldName": "may assign but not be released from liability under this Contract or", "fieldType": "CheckBox", "index": 35, "suggestedMapping": "General information"}, {"fieldName": "may not assign this", "fieldType": "CheckBox", "index": 36, "suggestedMapping": "General information"}, {"fieldName": "may assign and thereby be released from any further liability under", "fieldType": "CheckBox", "index": 37, "suggestedMapping": "General information"}, {"fieldName": "a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers", "fieldType": "CheckBox", "index": 38, "suggestedMapping": "Buyer information"}, {"fieldName": "b This Contract is contingent upon Buyer obtaining approval of a", "fieldType": "CheckBox", "index": 39, "suggestedMapping": "Buyer information"}, {"fieldName": "conventional", "fieldType": "CheckBox", "index": 40, "suggestedMapping": "General information"}, {"fieldName": "FHA", "fieldType": "CheckBox", "index": 41, "suggestedMapping": "General information"}, {"fieldName": "VA or", "fieldType": "CheckBox", "index": 42, "suggestedMapping": "General information"}, {"fieldName": "other", "fieldType": "CheckBox", "index": 43, "suggestedMapping": "General information"}, {"fieldName": "fixed", "fieldType": "CheckBox", "index": 44, "suggestedMapping": "General information"}, {"fieldName": "adjustable", "fieldType": "CheckBox", "index": 45, "suggestedMapping": "General information"}, {"fieldName": "fixed or adjustable rate in the Loan Amount See Paragraph", "fieldType": "CheckBox", "index": 46, "suggestedMapping": "Financial information"}, {"fieldName": "c Assumption of existing mortgage see rider for terms", "fieldType": "CheckBox", "index": 56, "suggestedMapping": "General information"}, {"fieldName": "d Purchase money note and mortgage to <PERSON><PERSON> see riders addenda or special clauses for terms", "fieldType": "CheckBox", "index": 57, "suggestedMapping": "Seller information"}, {"fieldName": "i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the", "fieldType": "CheckBox", "index": 60, "suggestedMapping": "Buyer information"}, {"fieldName": "ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing", "fieldType": "CheckBox", "index": 61, "suggestedMapping": "Buyer information"}, {"fieldName": "iii MIAMIDADEBROWARD REGIONAL PROVISION Seller shall furnish a copy of a prior owners policy", "fieldType": "CheckBox", "index": 67, "suggestedMapping": "Seller information"}, {"fieldName": "Buyer", "fieldType": "CheckBox", "index": 68, "suggestedMapping": "Buyer information"}, {"fieldName": "<PERSON><PERSON>", "fieldType": "CheckBox", "index": 69, "suggestedMapping": "Seller information"}, {"fieldName": "NA shall pay for a home warranty plan issued by", "fieldType": "CheckBox", "index": 70, "suggestedMapping": "General information"}, {"fieldName": "a Seller shall pay installments due prior to Closing and Buyer shall pay installments due after Closing", "fieldType": "CheckBox", "index": 72, "suggestedMapping": "Buyer information"}, {"fieldName": "b Seller shall pay the assessments in full prior to or at the time of Closing", "fieldType": "CheckBox", "index": 73, "suggestedMapping": "Seller information"}, {"fieldName": "K RESERVED", "fieldType": "CheckBox", "index": 106, "suggestedMapping": "General information"}, {"fieldName": "L RESERVED", "fieldType": "CheckBox", "index": 107, "suggestedMapping": "General information"}, {"fieldName": "M Defective Drywall", "fieldType": "CheckBox", "index": 108, "suggestedMapping": "General information"}, {"fieldName": "N Coastal Construction Control", "fieldType": "CheckBox", "index": 109, "suggestedMapping": "General information"}, {"fieldName": "O Insulation Disclosure", "fieldType": "CheckBox", "index": 110, "suggestedMapping": "General information"}, {"fieldName": "P Lead Paint Disclosure Pre1978", "fieldType": "CheckBox", "index": 111, "suggestedMapping": "General information"}, {"fieldName": "Q Housing for Older Persons", "fieldType": "CheckBox", "index": 112, "suggestedMapping": "General information"}, {"fieldName": "R Rezoning", "fieldType": "CheckBox", "index": 113, "suggestedMapping": "General information"}, {"fieldName": "S Lease Purchase Lease Option", "fieldType": "CheckBox", "index": 114, "suggestedMapping": "General information"}, {"fieldName": "A Condominium Rider", "fieldType": "CheckBox", "index": 115, "suggestedMapping": "General information"}, {"fieldName": "B Homeowners Assn", "fieldType": "CheckBox", "index": 116, "suggestedMapping": "General information"}, {"fieldName": "C Seller Financing", "fieldType": "CheckBox", "index": 117, "suggestedMapping": "Seller information"}, {"fieldName": "D Mortgage Assumption", "fieldType": "CheckBox", "index": 118, "suggestedMapping": "General information"}, {"fieldName": "E FHAVA Financing", "fieldType": "CheckBox", "index": 119, "suggestedMapping": "General information"}, {"fieldName": "F Appraisal Contingency", "fieldType": "CheckBox", "index": 120, "suggestedMapping": "General information"}, {"fieldName": "G Short Sale", "fieldType": "CheckBox", "index": 121, "suggestedMapping": "General information"}, {"fieldName": "H HomeownersFlood Ins", "fieldType": "CheckBox", "index": 122, "suggestedMapping": "General information"}, {"fieldName": "I", "fieldType": "CheckBox", "index": 123, "suggestedMapping": "General information"}, {"fieldName": "J", "fieldType": "CheckBox", "index": 124, "suggestedMapping": "General information"}, {"fieldName": "T PreClosing Occupancy", "fieldType": "CheckBox", "index": 125, "suggestedMapping": "General information"}, {"fieldName": "V Sale of Buyers Property", "fieldType": "CheckBox", "index": 126, "suggestedMapping": "Buyer information"}, {"fieldName": "W Backup Contract", "fieldType": "CheckBox", "index": 127, "suggestedMapping": "General information"}, {"fieldName": "Y Sellers Attorney App<PERSON>al", "fieldType": "CheckBox", "index": 128, "suggestedMapping": "Seller information"}, {"fieldName": "Z Buyers Attorney <PERSON><PERSON><PERSON><PERSON>", "fieldType": "CheckBox", "index": 129, "suggestedMapping": "Buyer information"}, {"fieldName": "BB Binding Arbitration", "fieldType": "CheckBox", "index": 130, "suggestedMapping": "General information"}, {"fieldName": "CC MiamiDade County", "fieldType": "CheckBox", "index": 131, "suggestedMapping": "General information"}, {"fieldName": "Seller counters Buyers offer to accept the counteroffer Buyer must sign or initial the counteroffered terms and", "fieldType": "CheckBox", "index": 151, "suggestedMapping": "Buyer information"}, {"fieldName": "<PERSON><PERSON> rejects Buyers offer", "fieldType": "CheckBox", "index": 152, "suggestedMapping": "Buyer information"}, {"fieldName": "Check Box97", "fieldType": "CheckBox", "index": 157, "suggestedMapping": "General information"}, {"fieldName": "Check Box99", "fieldType": "CheckBox", "index": 158, "suggestedMapping": "General information"}, {"fieldName": "Check Box101", "fieldType": "CheckBox", "index": 159, "suggestedMapping": "General information"}, {"fieldName": "Check Box102", "fieldType": "CheckBox", "index": 160, "suggestedMapping": "General information"}]}