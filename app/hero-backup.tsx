"use client"

import { useState, useRef } from "react"
import { AddressAutocomplete } from "@/components/forms/AddressAutocomplete"
import { PropertyDetail } from "@/lib/property-api"
import { ArrowRight, Shield, CheckCircle, Timer, MapPin } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

type ContractType = "Buyers" | "Sellers" | "Wholesalers"

export default function HeroBackup() {
  const [addressSelected, setAddressSelected] = useState(false)
  const [propertyDetails, setPropertyDetails] = useState<PropertyDetail | null>(null)
  const [contractType, setContractType] = useState<ContractType>("Buyers")
  const [autoData, setAutoData] = useState(true)
  const [assignable, setAssignable] = useState(false)
  const [autoSubmit, setAutoSubmit] = useState(false)
  const [addressValue, setAddressValue] = useState("")
  const heroRef = useRef<HTMLDivElement>(null)

  const handleAddressSelect = (address: string, details?: PropertyDetail) => {
    setAddressValue(address)
    setPropertyDetails(details || null)
    setAddressSelected(true)
  }

  const handleContractTypeChange = (type: ContractType) => {
    setContractType(type)
    if (type === "Wholesalers") {
      setAssignable(true)
    } else {
      setAssignable(false)
    }
  }

  return (
    <section className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-20" ref={heroRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4 leading-tight">
            <span className="text-gray-900">Create Professional</span>
            <span className="block text-[#006AFF] font-extrabold">Real Estate Purchase Contracts</span>
          </h1>
          <p className="text-xl text-gray-700 font-medium">
            Streamline all 50 state-compliant purchase agreements with smart data integration
          </p>
        </div>

        {/* Address Input - Main CTA */}
        {!addressSelected ? (
          <div className="max-w-3xl mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl border-2 border-[#006AFF]/20 p-8">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#006AFF] to-blue-700 rounded-2xl mb-4 shadow-lg">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Enter Property Address</h2>
                <p className="text-lg text-gray-600">Start by entering the address of the property</p>
              </div>
              
              <AddressAutocomplete
                onAddressSelect={handleAddressSelect}
                placeholder="123 Main Street, Miami, FL 33101"
                className="mb-6"
              />

              <div className="flex items-center gap-6 text-sm text-gray-500 justify-center">
                <span className="flex items-center gap-1">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Instant property data
                </span>
                <span className="flex items-center gap-1">
                  <Shield className="h-4 w-4 text-blue-500" />
                  State compliant forms
                </span>
                <span className="flex items-center gap-1">
                  <Timer className="h-4 w-4 text-purple-500" />
                  90 second generation
                </span>
              </div>
            </div>
          </div>
        ) : (
          /* Contract Configuration */
          <div className="max-w-3xl mx-auto">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              {/* Selected Address Display */}
              <div className="flex items-center gap-3 mb-6 pb-4 border-b border-gray-100">
                <MapPin className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{addressValue}</div>
                  <div className="text-sm text-gray-500">Property selected</div>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setAddressSelected(false)
                    setContractType("Buyers")
                    setPropertyDetails(null)
                    setAddressValue("")
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Change
                </Button>
              </div>

              {/* Property Details Display */}
              {propertyDetails && autoData && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                  <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Live Property Data Retrieved
                  </h3>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    {propertyDetails.county && (
                      <div>
                        <span className="text-blue-700 font-medium">County:</span>
                        <span className="text-blue-600 ml-2">{propertyDetails.county}</span>
                      </div>
                    )}
                    {propertyDetails.parcel_number && (
                      <div>
                        <span className="text-blue-700 font-medium">Parcel #:</span>
                        <span className="text-blue-600 ml-2">{propertyDetails.parcel_number}</span>
                      </div>
                    )}
                    {propertyDetails.owner_name && (
                      <div className="col-span-2">
                        <span className="text-blue-700 font-medium">Owner:</span>
                        <span className="text-blue-600 ml-2">{propertyDetails.owner_name}</span>
                      </div>
                    )}
                    {propertyDetails.estimated_value && (
                      <div>
                        <span className="text-blue-700 font-medium">Est. Value:</span>
                        <span className="text-blue-600 ml-2">${propertyDetails.estimated_value.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Contract Type Selection */}
              <div className="mb-6">
                <Label className="text-base font-semibold text-gray-900 mb-4 block">
                  Select Contract Type:
                </Label>
                <div className="grid grid-cols-3 gap-3">
                  {(["Buyers", "Sellers", "Wholesalers"] as ContractType[]).map((type) => (
                    <button
                      key={type}
                      onClick={() => handleContractTypeChange(type)}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        contractType === type
                          ? "border-[#006AFF] bg-blue-50 text-[#006AFF]"
                          : "border-gray-200 hover:border-gray-300 text-gray-700"
                      }`}
                    >
                      <div className="text-sm font-medium">{type}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Configuration Toggles */}
              <div className="space-y-4 pb-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-900">Auto-data</Label>
                    <p className="text-xs text-gray-500">Pre-fill fields with property data</p>
                  </div>
                  <Switch checked={autoData} onCheckedChange={setAutoData} />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className={`text-sm font-medium ${contractType !== "Wholesalers" ? "text-gray-400" : "text-gray-900"}`}>
                      Assignable
                    </Label>
                    <p className={`text-xs ${contractType !== "Wholesalers" ? "text-gray-400" : "text-gray-500"}`}>
                      Allow contract assignment
                    </p>
                  </div>
                  <Switch
                    checked={assignable && contractType === "Wholesalers"}
                    onCheckedChange={setAssignable}
                    disabled={contractType !== "Wholesalers"}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-900">Auto-Submit</Label>
                    <p className="text-xs text-gray-500">Generate after configuration</p>
                  </div>
                  <Switch checked={autoSubmit} onCheckedChange={setAutoSubmit} />
                </div>
              </div>

              {/* Generate Button */}
              <button className="w-full mt-6 flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-[#006AFF] to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl">
                Generate {contractType} Agreement
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}