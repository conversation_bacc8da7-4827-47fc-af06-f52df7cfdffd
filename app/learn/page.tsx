import { Button } from "@/components/ui/button"
import Link from "next/link"
import { BookOpen, FileText, Video, Users } from "lucide-react"

export default function LearnPage() {
  const resources = [
    {
      icon: BookOpen,
      title: "Beginner's Guide",
      description: "Everything you need to know about real estate investing",
      link: "#",
    },
    {
      icon: FileText,
      title: "Investment Strategies",
      description: "Learn different approaches to building your portfolio",
      link: "#",
    },
    {
      icon: Video,
      title: "Video Tutorials",
      description: "Watch step-by-step guides on using our platform",
      link: "#",
    },
    {
      icon: Users,
      title: "Community Forum",
      description: "Connect with other investors and share insights",
      link: "#",
    },
  ]

  const articles = [
    {
      title: "Understanding Real Estate Returns",
      description: "Learn how rental income and appreciation contribute to your overall returns.",
      readTime: "5 min read",
    },
    {
      title: "Diversification in Real Estate",
      description: "Why spreading your investments across multiple properties reduces risk.",
      readTime: "7 min read",
    },
    {
      title: "Tax Benefits of Real Estate Investing",
      description: "Discover the tax advantages available to real estate investors.",
      readTime: "10 min read",
    },
    {
      title: "Market Analysis: Where to Invest",
      description: "How we identify the best markets for rental property investments.",
      readTime: "8 min read",
    },
  ]

  const faqs = [
    {
      question: "What is fractional real estate investing?",
      answer: "Fractional real estate investing allows you to own a portion of a property rather than the entire property. This makes real estate investing more accessible and allows for better diversification.",
    },
    {
      question: "How do I earn money from my investments?",
      answer: "You earn money through two primary ways: monthly rental income distributions and long-term property appreciation when the property is sold.",
    },
    {
      question: "What are the risks involved?",
      answer: "Like all investments, real estate carries risks including market fluctuations, vacancy periods, and maintenance costs. We mitigate these through careful property selection and professional management.",
    },
    {
      question: "Can I sell my investment?",
      answer: "Yes, you can sell your shares on our secondary marketplace to other investors, subject to market demand and platform terms.",
    },
  ]

  return (
    <div className="py-12">
      <section className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-4xl sm:text-5xl font-bold tracking-tight">
            Learn Real Estate Investing
          </h1>
          <p className="mt-6 text-lg text-muted-foreground">
            Build your knowledge with our comprehensive resources, guides, and expert insights.
          </p>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Educational Resources
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {resources.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                  <resource.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{resource.title}</h3>
                <p className="text-muted-foreground text-sm">{resource.description}</p>
              </a>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Latest Articles
          </h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {articles.map((article, index) => (
              <article key={index} className="border-b pb-6">
                <h3 className="text-xl font-semibold mb-2 hover:text-primary cursor-pointer">
                  {article.title}
                </h3>
                <p className="text-muted-foreground mb-2">{article.description}</p>
                <span className="text-sm text-muted-foreground">{article.readTime}</span>
              </article>
            ))}
          </div>
          <div className="text-center mt-8">
            <Button variant="outline">View All Articles</Button>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                <p className="text-muted-foreground">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Put Your Knowledge to Work?
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Start investing in real estate with confidence
          </p>
          <Link href="/signup">
            <Button size="lg">
              Start Investing Today
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}