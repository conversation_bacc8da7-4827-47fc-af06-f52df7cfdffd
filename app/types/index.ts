export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  fullAddress: string;
}

// Property type enum
export type PropertyType = 
  | 'SINGLE_FAMILY' 
  | 'CONDO' 
  | 'TOWNHOUSE' 
  | 'MULTI_FAMILY' 
  | 'LAND' 
  | 'COMMERCIAL';

export interface PropertyDetails {
  address: Address;
  listingPrice?: number;
  legalDescription?: string;
  propertyType?: PropertyType;
  yearBuilt?: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  lotSize?: number;
  mls?: string;
  // Additional fields from Real Estate API
  propertyId?: string;
  apn?: string;
  county?: string;
  latitude?: number;
  longitude?: number;
  vacant?: boolean;
  absenteeOwner?: boolean;
  ownerOccupied?: boolean;
  estimatedValue?: number;
  assessedValue?: number;
  taxAmount?: number;
  lotSquareFeet?: number;
  acres?: number;
  subdivision?: string;
  zoning?: string;
  landUse?: string;
  // Owner information
  ownerName?: string;
  ownerType?: string;
  yearsOwned?: number;
  // Property characteristics
  stories?: number;
  rooms?: number;
  units?: number;
  garage?: boolean;
  pool?: boolean;
  fireplace?: boolean;
  basement?: boolean;
  attic?: boolean;
  centralAir?: boolean;
  heating?: string;
  cooling?: string;
  roofType?: string;
  exteriorWalls?: string;
  foundation?: string;
  // Market data
  marketValue?: number;
  pricePerSquareFoot?: number;
  daysOnMarket?: number;
  priceHistory?: Array<{
    date: string;
    price: number;
    event: string;
  }>;
  // Neighborhood data
  walkScore?: number;
  schoolDistrict?: string;
  crimeRate?: string;
  floodZone?: string;
  earthquakeZone?: string;
  // Environmental
  leadPaint?: boolean;
  asbestos?: boolean;
  radon?: boolean;
  mold?: boolean;
  // Utilities
  sewer?: string;
  water?: string;
  gas?: string;
  electric?: string;
  internet?: string;
  cable?: string;
}

export interface BuyerInfo {
  name: string;
  email: string;
  phone: string;
  address: Address;
  preApprovalAmount?: number;
  lenderName?: string;
  agentName?: string;
  agentPhone?: string;
  agentEmail?: string;
}

export interface OfferDetails {
  offerPrice: number;
  earnestMoney: number;
  downPayment: number;
  loanAmount: number;
  closingDate: string;
  inspectionPeriod: number;
  appraisalContingency: boolean;
  financingContingency: boolean;
  saleOfHomeContinency: boolean;
  additionalTerms?: string;
}

export interface FormData {
  step: number;
  propertyDetails?: PropertyDetails;
  buyerInfo?: BuyerInfo;
  offerDetails?: OfferDetails;
  agreementType?: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL';
  requiredForms?: string[];
  email?: string;
  devMode?: boolean;
}

export interface AutoCompleteResult {
  id: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  fullAddress: string;
}

export interface VerifiedAddress {
  verified: boolean;
  confidence: number;
  address: Address;
  propertyId?: string;
}

export interface RequiredForm {
  id: string;
  name: string;
  description: string;
  required: boolean;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  officialAgreements: number;
  unofficialAgreements: number;
  features: string[];
}
