"use client"

import { useState, useRef } from "react"
import { ArrowRight, Shield, CheckCircle, Timer, FileText, Users, Zap, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Footer } from "@/components/Footer"
import { AnimatedSections } from "@/components/AnimatedSections"
import { AnimatedJourney } from "@/components/hero/AnimatedJourney"

export default function HomePage() {
  const [showLogin, setShowLogin] = useState(false)
  const formRef = useRef<HTMLDivElement>(null)

  const handleGetStarted = () => {
    setShowLogin(true)
  }

  const handleLogin = () => {
    // Redirect to FormFlorida multiform
    window.location.href = '/pdf-form-filler'
  }

  // Handler to scroll/focus the form section for AnimatedSections
  const handleFocusForm = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-full text-[#006AFF] text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>PROFESSIONAL REAL ESTATE DOCUMENTS</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              <span className="text-slate-900">Create Florida</span>
              <span className="block text-[#006AFF]">Purchase Agreements</span>
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto mb-12 leading-relaxed">
              Generate legally compliant Florida purchase agreements in minutes.
              <span className="text-[#006AFF] font-semibold"> Built by licensed brokers for real estate professionals.</span>
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button
                onClick={handleGetStarted}
                className="bg-[#006AFF] hover:bg-[#0056CC] text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                className="border-[#006AFF] text-[#006AFF] hover:bg-[#006AFF] hover:text-white px-8 py-4 text-lg font-semibold rounded-lg transition-all"
              >
                Watch Demo
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-3 p-4 bg-slate-50 rounded-lg border border-slate-200">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <span className="text-slate-700 font-medium">Licensed Broker Built</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 bg-slate-50 rounded-lg border border-slate-200">
                <Shield className="h-6 w-6 text-[#006AFF]" />
                <span className="text-slate-700 font-medium">Florida State Compliant</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 bg-slate-50 rounded-lg border border-slate-200">
                <Timer className="h-6 w-6 text-orange-600" />
                <span className="text-slate-700 font-medium">Ready in 5 Minutes</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Animated Product Demo Sections */}
      <section className="py-12 bg-white border-b border-[#006AFF]/10">
        <div className="container mx-auto px-4">
          <AnimatedSections onAddressInputFocus={handleFocusForm} />
        </div>
      </section>

      {/* Section 3: Animated Journey */}
      <AnimatedJourney />

      {/* CTA Section */}
      <section className="py-20 bg-[#006AFF]">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Streamline Your Real Estate Business?
            </h2>
            <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto">
              Join the growing community of Florida real estate professionals who save time and close more deals with FormFlorida
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleGetStarted}
                className="bg-white text-[#006AFF] hover:bg-slate-100 px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#006AFF] px-8 py-4 text-lg font-semibold rounded-lg transition-all"
              >
                Schedule Demo
              </Button>
            </div>

            <p className="text-blue-200 mt-8 text-sm">
              No credit card required • Free trial • Professional support included
            </p>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      {showLogin && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-8 max-w-md w-full">
            <h3 className="text-2xl font-bold text-slate-900 mb-6 text-center">
              Access FormFlorida
            </h3>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Email</label>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleLogin}
                className="flex-1 bg-[#006AFF] hover:bg-[#0056CC] text-white py-3 font-semibold rounded-lg transition-all"
              >
                Access FormFlorida
              </Button>
              <Button
                onClick={() => setShowLogin(false)}
                variant="outline"
                className="px-6 py-3 font-semibold rounded-lg transition-all"
              >
                Cancel
              </Button>
            </div>

            <p className="text-center text-sm text-slate-600 mt-4">
              Don't have an account? <span className="text-[#006AFF] font-semibold cursor-pointer">Sign up free</span>
            </p>
          </div>
        </div>
      )}

      <Footer />
    </div>
  )
}