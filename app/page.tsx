"use client"

import { useState, useRef } from "react"
import { AddressAutocomplete } from "@/components/forms/AddressAutocomplete"
import { LivePropertyIntelligence } from "@/components/sections/LivePropertyIntelligence"
import { SmartFormPrePopulation } from "@/components/sections/SmartFormPrePopulation"
import { FloridaRequirementsGenerator } from "@/components/sections/FloridaRequirementsGenerator"
import { SectionConnector } from "@/components/sections/SectionConnector"
import { TrustBadgesSection } from "@/components/sections/TrustBadgesSection"
import { PricingSection } from "@/components/sections/PricingSection"
import { StickyGetStarted } from "@/components/StickyGetStarted"
import { AnimatedJourney } from "@/components/hero/AnimatedJourney"
import { ProcessSteps } from "@/components/sections/ProcessSteps"
import { Footer } from "@/components/Footer"
import { PropertyDetail } from "@/lib/property-api"
import { ArrowRight, Shield, CheckCircle, Timer, MapPin, Sparkles } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

type ContractType = "Buyers" | "Sellers" | "Wholesalers"

export default function HomePage() {
  const [addressSelected, setAddressSelected] = useState(false)
  const [propertyDetails, setPropertyDetails] = useState<PropertyDetail | null>(null)
  const [contractType, setContractType] = useState<ContractType>("Buyers")
  const [autoData, setAutoData] = useState(true)
  const [assignable, setAssignable] = useState(false)
  const [autoSubmit, setAutoSubmit] = useState(false)
  const [addressValue, setAddressValue] = useState("")
  const heroRef = useRef<HTMLDivElement>(null)

  const handleAddressSelect = (address: string, details?: PropertyDetail) => {
    setAddressValue(address)
    setPropertyDetails(details || null)
    setAddressSelected(true)
  }

  const handleContractTypeChange = (type: ContractType) => {
    setContractType(type)
    if (type === "Wholesalers") {
      setAssignable(true)
    } else {
      setAssignable(false)
    }
  }

  const scrollToHero = () => {
    if (heroRef.current) {
      heroRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  return (
    <div className="min-h-[calc(100vh-4rem)]">
      {/* Hero Section with Address Input */}
      <section className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-20 relative overflow-hidden border-b border-amber-600/20" ref={heroRef}>
        {/* Professional document pattern background */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-300 text-sm font-semibold mb-8 backdrop-blur-sm">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
              <span>LICENSED REAL ESTATE BROKER</span>
              <div className="w-1 h-4 bg-amber-600/50"></div>
              <span>PROFESSIONAL GRADE DOCUMENTS</span>
              <div className="w-1 h-4 bg-amber-600/50"></div>
              <span>50-STATE COMPLIANT</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight font-serif">
              <span className="text-white">Professional</span>
              <span className="block text-transparent bg-gradient-to-r from-amber-400 via-yellow-300 to-amber-500 bg-clip-text font-extrabold">Real Estate Purchase Agreements</span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 font-medium max-w-4xl mx-auto leading-relaxed">
              Generate legally compliant purchase agreements with intelligent property data integration.
              <span className="text-amber-300 font-semibold"> Built by licensed brokers for real estate professionals.</span>
            </p>
          </div>

          {/* Address Input - Main CTA */}
          {!addressSelected ? (
            <div className="max-w-5xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-lg shadow-2xl border-2 border-amber-600/20 p-10 relative">
                {/* Document header styling */}
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600"></div>

                <div className="text-center mb-10">
                  <div className="inline-flex items-center gap-2 text-slate-600 text-sm font-semibold mb-4">
                    <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">1</span>
                    </div>
                    <span>PROPERTY IDENTIFICATION</span>
                  </div>
                  <h2 className="text-3xl font-bold text-slate-800 mb-3 font-serif">Enter Property Address</h2>
                  <p className="text-lg text-slate-600">Begin your purchase agreement by identifying the subject property</p>
                </div>

                <AddressAutocomplete
                  onAddressSelect={handleAddressSelect}
                  placeholder="Enter property address (e.g., 123 Main Street, Miami, FL 33101)"
                  className="mb-10"
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                  <div className="flex items-center gap-3 text-slate-700 bg-slate-50 rounded-lg px-4 py-4 border border-slate-200">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold">Instant Property Data</div>
                      <div className="text-xs text-slate-500">Owner, value, legal description</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-slate-700 bg-slate-50 rounded-lg px-4 py-4 border border-slate-200">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <Shield className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold">Legal Compliance</div>
                      <div className="text-xs text-slate-500">State-specific requirements</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-slate-700 bg-slate-50 rounded-lg px-4 py-4 border border-slate-200">
                    <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                      <Timer className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold">Professional Speed</div>
                      <div className="text-xs text-slate-500">Complete in 90 seconds</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Contract Configuration */
            <div className="max-w-5xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-lg shadow-2xl border-2 border-amber-600/20 p-10 relative">
                {/* Document header styling */}
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600"></div>

                {/* Selected Address Display */}
                <div className="flex items-center gap-4 mb-8 pb-6 border-b border-slate-200">
                  <div className="w-12 h-12 bg-amber-600 rounded-lg flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-slate-500 font-semibold uppercase tracking-wide">SUBJECT PROPERTY</div>
                    <div className="font-semibold text-slate-800 text-xl font-serif">{addressValue}</div>
                    <div className="text-sm text-slate-600">Property identified and verified</div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setAddressSelected(false)
                      setContractType("Buyers")
                      setPropertyDetails(null)
                      setAddressValue("")
                    }}
                    className="text-slate-600 hover:text-slate-800 border-slate-300 hover:border-slate-400 bg-white"
                  >
                    Change Property
                  </Button>
                </div>

                {/* Property Details Display */}
                {propertyDetails && autoData && (
                  <div className="mb-8 p-6 bg-green-50 border-2 border-green-200 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-4 flex items-center gap-2 text-lg">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      Property Information Retrieved
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {propertyDetails.county && (
                        <div className="bg-white rounded-lg p-4 border border-green-100">
                          <div className="text-slate-500 font-medium text-xs uppercase tracking-wide">County</div>
                          <div className="text-slate-800 font-semibold text-base">{propertyDetails.county}</div>
                        </div>
                      )}
                      {propertyDetails.parcel_number && (
                        <div className="bg-white rounded-lg p-4 border border-green-100">
                          <div className="text-slate-500 font-medium text-xs uppercase tracking-wide">Parcel Number</div>
                          <div className="text-slate-800 font-semibold text-base">{propertyDetails.parcel_number}</div>
                        </div>
                      )}
                      {propertyDetails.owner_name && (
                        <div className="col-span-2 bg-white rounded-lg p-4 border border-green-100">
                          <div className="text-slate-500 font-medium text-xs uppercase tracking-wide">Current Owner</div>
                          <div className="text-slate-800 font-semibold text-base">{propertyDetails.owner_name}</div>
                        </div>
                      )}
                      {propertyDetails.estimated_value && (
                        <div className="col-span-2 bg-white rounded-lg p-4 border border-green-100">
                          <div className="text-slate-500 font-medium text-xs uppercase tracking-wide">Estimated Market Value</div>
                          <div className="text-green-700 font-bold text-xl">${propertyDetails.estimated_value.toLocaleString()}</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Contract Type Selection */}
                <div className="mb-8">
                  <div className="flex items-center gap-2 mb-6">
                    <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">2</span>
                    </div>
                    <Label className="text-lg font-semibold text-slate-800 font-serif">
                      Agreement Type Selection
                    </Label>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    {(["Buyers", "Sellers", "Wholesalers"] as ContractType[]).map((type) => (
                      <button
                        key={type}
                        onClick={() => handleContractTypeChange(type)}
                        className={`p-6 rounded-lg border-2 transition-all duration-300 ${
                          contractType === type
                            ? "border-amber-600 bg-amber-50 text-slate-800 shadow-lg"
                            : "border-slate-300 hover:border-slate-400 text-slate-600 hover:text-slate-800 bg-white hover:bg-slate-50"
                        }`}
                      >
                        <div className="text-base font-semibold">{type} Agreement</div>
                        <div className="text-xs mt-2 opacity-75">
                          {type === "Buyers" && "Purchase & Sale Agreement"}
                          {type === "Sellers" && "Listing & Sale Agreement"}
                          {type === "Wholesalers" && "Assignable Purchase Contract"}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Configuration Options */}
                <div className="space-y-6 pb-8 border-b border-slate-200">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">3</span>
                    </div>
                    <Label className="text-lg font-semibold text-slate-800 font-serif">
                      Document Configuration
                    </Label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <div>
                      <Label className="text-base font-medium text-slate-800">Automatic Data Population</Label>
                      <p className="text-sm text-slate-600">Pre-fill agreement fields with retrieved property data</p>
                    </div>
                    <Switch checked={autoData} onCheckedChange={setAutoData} />
                  </div>

                  <div className={`flex items-center justify-between p-4 rounded-lg border transition-all ${
                    contractType !== "Wholesalers"
                      ? "bg-slate-100 border-slate-200 opacity-50"
                      : "bg-slate-50 border-slate-200"
                  }`}>
                    <div>
                      <Label className={`text-base font-medium ${contractType !== "Wholesalers" ? "text-slate-500" : "text-slate-800"}`}>
                        Assignment Rights
                      </Label>
                      <p className={`text-sm ${contractType !== "Wholesalers" ? "text-slate-400" : "text-slate-600"}`}>
                        Include assignment and delegation clauses
                      </p>
                    </div>
                    <Switch
                      checked={assignable && contractType === "Wholesalers"}
                      onCheckedChange={setAssignable}
                      disabled={contractType !== "Wholesalers"}
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <div>
                      <Label className="text-base font-medium text-slate-800">Immediate Generation</Label>
                      <p className="text-sm text-slate-600">Generate document immediately upon completion</p>
                    </div>
                    <Switch checked={autoSubmit} onCheckedChange={setAutoSubmit} />
                  </div>
                </div>

                {/* Generate Button */}
                <button className="w-full mt-8 flex items-center justify-center gap-4 px-8 py-6 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-bold text-lg rounded-lg hover:from-amber-700 hover:to-yellow-700 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105 group">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold">✓</span>
                    </div>
                    <span>Generate Professional {contractType} Agreement</span>
                  </div>
                  <ArrowRight className="h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </button>

                <div className="text-center mt-6 space-y-2">
                  <p className="text-slate-600 text-sm font-medium">
                    ⚡ Professional document generation in <span className="text-amber-600 font-bold">90 seconds</span>
                  </p>
                  <p className="text-slate-500 text-xs">
                    Legally compliant • State-specific requirements • Licensed broker reviewed
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Trust & Credibility Section */}
      <TrustBadgesSection />

      {/* How It Works - Step by Step Process */}
      <ProcessSteps />

      {/* Problem & Solution Explanation */}
      <AnimatedJourney />

      {/* Live Demo Sections - Show the product in action */}
      <LivePropertyIntelligence />
      <SmartFormPrePopulation />
      <FloridaRequirementsGenerator />

      {/* Pricing - Close the deal */}
      <PricingSection />

      {/* Sticky Get Started Button */}
      <StickyGetStarted onClick={scrollToHero} />
    </div>
  )
}