"use client"

import { useState, useRef } from "react"
import { ArrowRight, Shield, CheckCircle, Timer, FileText, Users, Zap, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

import { AutocompleteSection } from "@/components/sections/AutocompleteSection"
import { SmartFormSection } from "@/components/sections/SmartFormSection"

export default function HomePage() {
  const [showLogin, setShowLogin] = useState(false)
  const formRef = useRef<HTMLDivElement>(null)

  const handleGetStarted = () => {
    setShowLogin(true)
  }

  const handleLogin = () => {
    // Redirect to FormFlorida multiform
    window.location.href = '/pdf-form-filler'
  }

  // Handler to scroll/focus the form section for AnimatedSections
  const handleFocusForm = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-white via-blue-50/30 to-white py-20 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23006AFF' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            {/* Social Proof Badge */}
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-full text-[#006AFF] text-sm font-semibold mb-8 backdrop-blur-sm">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>TRUSTED BY 2,500+ FLORIDA REALTORS</span>
              <div className="w-1 h-4 bg-[#006AFF]/50"></div>
              <span>⭐ 4.9/5 RATING</span>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              <span className="text-slate-900">Generate Florida</span>
              <span className="block text-[#006AFF]">Purchase Agreements</span>
              <span className="block text-slate-900 text-3xl md:text-4xl font-normal mt-2">in 3 Minutes, Not 3 Hours</span>
            </h1>

            {/* Value Proposition */}
            <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto mb-8 leading-relaxed">
              Stop wasting hours on paperwork. Our AI-powered platform generates legally compliant Florida purchase agreements instantly.
              <span className="block mt-2 text-[#006AFF] font-semibold">Save 45+ minutes per deal. Built by licensed brokers.</span>
            </p>

            {/* Urgency/Scarcity */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 border border-orange-200 rounded-lg text-orange-700 text-sm font-medium mb-8">
              <Timer className="h-4 w-4" />
              <span>🔥 Limited Time: Free for your first 5 agreements</span>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button
                onClick={handleGetStarted}
                className="bg-[#006AFF] hover:bg-[#0056CC] text-white px-10 py-5 text-xl font-bold rounded-xl shadow-2xl hover:shadow-3xl transition-all transform hover:scale-105 relative overflow-hidden group"
              >
                <span className="relative z-10">Start Free Trial</span>
                <ArrowRight className="ml-3 h-6 w-6 relative z-10 group-hover:translate-x-1 transition-transform" />
                <div className="absolute inset-0 bg-gradient-to-r from-[#006AFF] to-[#0056CC] opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </Button>
              <Button
                variant="outline"
                className="border-2 border-[#006AFF] text-[#006AFF] hover:bg-[#006AFF] hover:text-white px-8 py-5 text-lg font-semibold rounded-xl transition-all"
              >
                <span className="mr-2">▶</span>
                Watch 2-Min Demo
              </Button>
            </div>

            {/* Risk Reversal */}
            <p className="text-sm text-slate-500 mb-12">
              ✅ No credit card required • ✅ Cancel anytime • ✅ 30-day money-back guarantee
            </p>

            {/* Enhanced Trust Indicators with Numbers */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
              <div className="flex flex-col items-center p-6 bg-white rounded-xl border border-slate-200 shadow-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-green-600 mb-1">100%</div>
                <span className="text-slate-700 font-medium text-center">State Compliant</span>
              </div>
              <div className="flex flex-col items-center p-6 bg-white rounded-xl border border-slate-200 shadow-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  <Shield className="h-6 w-6 text-[#006AFF]" />
                </div>
                <div className="text-2xl font-bold text-[#006AFF] mb-1">Licensed</div>
                <span className="text-slate-700 font-medium text-center">Broker Built</span>
              </div>
              <div className="flex flex-col items-center p-6 bg-white rounded-xl border border-slate-200 shadow-lg">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-3">
                  <Timer className="h-6 w-6 text-orange-600" />
                </div>
                <div className="text-2xl font-bold text-orange-600 mb-1">3 Min</div>
                <span className="text-slate-700 font-medium text-center">Average Time</span>
              </div>
              <div className="flex flex-col items-center p-6 bg-white rounded-xl border border-slate-200 shadow-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-3">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-purple-600 mb-1">2,500+</div>
                <span className="text-slate-700 font-medium text-center">Happy Realtors</span>
              </div>
            </div>

            {/* Customer Logos/Testimonial Preview */}
            <div className="mt-16 pt-8 border-t border-slate-200">
              <p className="text-sm text-slate-500 mb-6">Trusted by top Florida real estate professionals</p>
              <div className="flex items-center justify-center gap-8 opacity-60">
                <div className="text-lg font-bold text-slate-400">Coldwell Banker</div>
                <div className="text-lg font-bold text-slate-400">RE/MAX</div>
                <div className="text-lg font-bold text-slate-400">Keller Williams</div>
                <div className="text-lg font-bold text-slate-400">Century 21</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Autocomplete Demo */}
      <AutocompleteSection onAddressInputFocus={handleFocusForm} />

      {/* Section 3: Smart Form Pre-Population */}
      <SmartFormSection />

      {/* Enhanced CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#006AFF] via-[#0056CC] to-[#004499] relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-white rounded-full"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-5xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Side - Value Proposition */}
              <div className="text-left">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 rounded-full text-white text-sm font-semibold mb-6">
                  <Zap className="h-4 w-4" />
                  <span>LIMITED TIME OFFER</span>
                </div>

                <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                  Stop Losing Money on Slow Paperwork
                </h2>

                <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                  Every hour you spend on manual paperwork is an hour you're not closing deals.
                  <span className="text-white font-semibold block mt-2">Join 2,500+ Florida realtors saving 45+ minutes per agreement.</span>
                </p>

                {/* ROI Calculator */}
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-8 border border-white/20">
                  <h3 className="text-white font-bold mb-4">💰 Your Monthly Savings Calculator</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-blue-100">
                      <div>Agreements per month: <span className="text-white font-bold">20</span></div>
                      <div>Time saved per agreement: <span className="text-white font-bold">45 min</span></div>
                    </div>
                    <div className="text-blue-100">
                      <div>Total time saved: <span className="text-white font-bold">15 hours</span></div>
                      <div>Value at $100/hour: <span className="text-white font-bold text-lg">$1,500/month</span></div>
                    </div>
                  </div>
                </div>

                {/* Social Proof */}
                <div className="flex items-center gap-4 mb-8">
                  <div className="flex -space-x-2">
                    <div className="w-10 h-10 bg-white rounded-full border-2 border-white flex items-center justify-center text-sm font-bold text-[#006AFF]">JD</div>
                    <div className="w-10 h-10 bg-white rounded-full border-2 border-white flex items-center justify-center text-sm font-bold text-[#006AFF]">SM</div>
                    <div className="w-10 h-10 bg-white rounded-full border-2 border-white flex items-center justify-center text-sm font-bold text-[#006AFF]">AL</div>
                    <div className="w-10 h-10 bg-white rounded-full border-2 border-white flex items-center justify-center text-sm font-bold text-[#006AFF]">+2K</div>
                  </div>
                  <div className="text-white">
                    <div className="font-semibold">2,500+ realtors joined this month</div>
                    <div className="text-blue-200 text-sm">⭐⭐⭐⭐⭐ 4.9/5 average rating</div>
                  </div>
                </div>
              </div>

              {/* Right Side - CTA Form */}
              <div className="bg-white rounded-2xl p-8 shadow-2xl">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">Start Your Free Trial</h3>
                  <p className="text-slate-600">Generate your first 5 agreements free</p>
                </div>

                <div className="space-y-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Work Email</label>
                    <input
                      type="email"
                      className="w-full px-4 py-3 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Brokerage Name</label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                      placeholder="Your Brokerage"
                    />
                  </div>
                </div>

                <Button
                  onClick={handleGetStarted}
                  className="w-full bg-[#006AFF] hover:bg-[#0056CC] text-white py-4 text-lg font-bold rounded-lg shadow-lg hover:shadow-xl transition-all mb-4"
                >
                  Start Free Trial - No Credit Card
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>

                <div className="text-center space-y-2">
                  <p className="text-xs text-slate-500">
                    ✅ Free for first 5 agreements • ✅ Cancel anytime • ✅ Setup in 2 minutes
                  </p>
                  <p className="text-xs text-slate-400">
                    By signing up, you agree to our Terms of Service and Privacy Policy
                  </p>
                </div>

                {/* Urgency Timer */}
                <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg text-center">
                  <div className="text-orange-700 font-semibold text-sm">🔥 Limited Time Offer Ends Soon</div>
                  <div className="text-orange-600 text-xs mt-1">Join before price increases to $97/month</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      {showLogin && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-8 max-w-md w-full">
            <h3 className="text-2xl font-bold text-slate-900 mb-6 text-center">
              Access FormFlorida
            </h3>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Email</label>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleLogin}
                className="flex-1 bg-[#006AFF] hover:bg-[#0056CC] text-white py-3 font-semibold rounded-lg transition-all"
              >
                Access FormFlorida
              </Button>
              <Button
                onClick={() => setShowLogin(false)}
                variant="outline"
                className="px-6 py-3 font-semibold rounded-lg transition-all"
              >
                Cancel
              </Button>
            </div>

            <p className="text-center text-sm text-slate-600 mt-4">
              Don't have an account? <span className="text-[#006AFF] font-semibold cursor-pointer">Sign up free</span>
            </p>
          </div>
        </div>
      )}

    </div>
  )
}