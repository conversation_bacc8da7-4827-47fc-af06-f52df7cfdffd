import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { CheckCircle, Search, Wallet, Home, TrendingUp } from "lucide-react"

export default function HowItWorksPage() {
  const steps = [
    {
      icon: Search,
      title: "Browse Properties",
      description: "Explore our carefully vetted portfolio of rental properties across the country. Each property includes detailed financials, location insights, and expected returns.",
    },
    {
      icon: Wallet,
      title: "Invest Your Amount",
      description: "Choose how much you want to invest, starting from just $100. You can diversify across multiple properties to reduce risk.",
    },
    {
      icon: Home,
      title: "Own Real Estate",
      description: "Once funded, you become a fractional owner of the property. Your ownership is proportional to your investment amount.",
    },
    {
      icon: TrendingUp,
      title: "Earn Returns",
      description: "Receive your share of rental income monthly and benefit from property appreciation over time. Track everything in your dashboard.",
    },
  ]

  const benefits = [
    "No property management hassles",
    "Professional tenant screening",
    "Automated rent collection",
    "Regular property maintenance",
    "Transparent financial reporting",
    "Easy portfolio management",
  ]

  return (
    <div className="py-12">
      <section className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-4xl sm:text-5xl font-bold tracking-tight">
            How PropBolt Works
          </h1>
          <p className="mt-6 text-lg text-muted-foreground">
            Real estate investing simplified. From browsing properties to earning returns, 
            we've made the entire process seamless and accessible.
          </p>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Your Journey to Real Estate Investment
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary text-white mb-4">
                    <step.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                  <p className="text-muted-foreground">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-[60%] w-full h-0.5 bg-gray-300" />
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">
                We Handle Everything
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Unlike traditional real estate investing, PropBolt takes care of all the 
                complexities. You simply invest and earn returns while we manage everything else.
              </p>
              <ul className="space-y-3">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="bg-gray-50 rounded-lg p-8">
              <h3 className="text-2xl font-bold mb-4">
                Example Investment
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Your Investment</span>
                  <span className="font-semibold">$5,000</span>
                </div>
                <div className="flex justify-between">
                  <span>Expected Annual Return</span>
                  <span className="font-semibold">10%</span>
                </div>
                <div className="flex justify-between">
                  <span>Monthly Rental Income</span>
                  <span className="font-semibold">$25</span>
                </div>
                <div className="flex justify-between">
                  <span>Annual Appreciation</span>
                  <span className="font-semibold">$250</span>
                </div>
                <hr />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Annual Return</span>
                  <span className="text-primary">$500</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Start Investing?
          </h2>
          <p className="text-lg mb-8 opacity-90">
            Join thousands of investors who are building wealth through real estate
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" variant="secondary">
                Get Started Now
              </Button>
            </Link>
            <Link href="/learn">
              <Button size="lg" variant="outline" className="bg-transparent text-white border-white hover:bg-white hover:text-primary">
                Learn More
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}