// Auto-generated TypeScript definitions for Purchase Agreement Forms
// Generated on: 2025-06-26T18:35:54.491Z

export interface FormOption {
  value: string;
  label: string;
}

export interface ConditionalLogic {
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'gte' | 'lte' | 'gt' | 'lt';
  value: string | string[];
  conditions?: ConditionalLogic[];
}

export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'radio' | 'checkbox' | 'select' | 'date' | 'currency' | 'number' | 'address' | 'info';
  required: boolean;
  options?: FormOption[];
  conditional?: ConditionalLogic;
  placeholder?: string;
  helpText?: string;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  rows?: number;
  defaultValue?: string | number;
}

export interface FormSection {
  id: string;
  title: string;
  fields: FormField[];
}

export interface PurchaseAgreementForm {
  formTitle: string;
  formId: string;
  stateCode: string;
  stateName: string;
  sections: FormSection[];
  fieldTypes?: Record<string, any>;
  conditionalOperators?: Record<string, string>;
  validation?: Record<string, string>;
}

export interface StateInfo {
  name: string;
  abbr: string;
  formId: string;
  formFile: string;
  apiEndpoint: string;
  routePath: string;
}

export interface FormsIndex {
  title: string;
  description: string;
  version: string;
  generatedAt: string;
  totalStates: number;
  states: StateInfo[];
  fieldTypes: Record<string, any>;
  commonSections: string[];
}

// State abbreviations type
export type StateAbbr = 'AL' | 'AK' | 'AZ' | 'AR' | 'CA' | 'CO' | 'CT' | 'DE' | 'FL' | 'GA' | 'HI' | 'ID' | 'IL' | 'IN' | 'IA' | 'KS' | 'KY' | 'LA' | 'ME' | 'MD' | 'MA' | 'MI' | 'MN' | 'MS' | 'MO' | 'MT' | 'NE' | 'NV' | 'NH' | 'NJ' | 'NM' | 'NY' | 'NC' | 'ND' | 'OH' | 'OK' | 'OR' | 'PA' | 'RI' | 'SC' | 'SD' | 'TN' | 'TX' | 'UT' | 'VT' | 'VA' | 'WA' | 'WV' | 'WI' | 'WY';

// State names type  
export type StateName = 'Alabama' | 'Alaska' | 'Arizona' | 'Arkansas' | 'California' | 'Colorado' | 'Connecticut' | 'Delaware' | 'Florida' | 'Georgia' | 'Hawaii' | 'Idaho' | 'Illinois' | 'Indiana' | 'Iowa' | 'Kansas' | 'Kentucky' | 'Louisiana' | 'Maine' | 'Maryland' | 'Massachusetts' | 'Michigan' | 'Minnesota' | 'Mississippi' | 'Missouri' | 'Montana' | 'Nebraska' | 'Nevada' | 'New Hampshire' | 'New Jersey' | 'New Mexico' | 'New York' | 'North Carolina' | 'North Dakota' | 'Ohio' | 'Oklahoma' | 'Oregon' | 'Pennsylvania' | 'Rhode Island' | 'South Carolina' | 'South Dakota' | 'Tennessee' | 'Texas' | 'Utah' | 'Vermont' | 'Virginia' | 'Washington' | 'West Virginia' | 'Wisconsin' | 'Wyoming';
