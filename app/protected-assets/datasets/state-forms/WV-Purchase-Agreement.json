{"formTitle": "West Virginia Residential Purchase and Sale Agreement", "formId": "wv_purchase_agreement", "stateCode": "WV", "stateName": "West Virginia", "sections": [{"id": "property_type", "title": "Property Type", "fields": [{"id": "property_type", "label": "What type of property is being purchased?", "type": "radio", "required": true, "options": [{"value": "single_family", "label": "Single Family Home"}, {"value": "condo", "label": "Condominium"}, {"value": "townhouse", "label": "Townhouse"}, {"value": "multi_family", "label": "Multi-Family (2-4 units)"}, {"value": "land", "label": "Vacant Land"}, {"value": "mobile_home", "label": "Mobile/Manufactured Home"}, {"value": "other", "label": "Other"}]}, {"id": "property_description", "label": "Please describe the property:", "type": "text", "required": true, "conditional": {"field": "property_type", "operator": "equals", "value": "other"}}]}, {"id": "buyer_information", "title": "Buyer Information", "fields": [{"id": "buyer_count", "label": "How many buyers?", "type": "select", "required": true, "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}, {"value": "5", "label": "5"}, {"value": "6", "label": "6"}, {"value": "7", "label": "7"}, {"value": "8", "label": "8"}]}, {"id": "buyer_1_name", "label": "Buyer's name:", "type": "text", "required": true}, {"id": "buyer_1_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}]}, {"id": "buyer_1_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"field": "buyer_1_entity_type", "operator": "equals", "value": "entity"}}, {"id": "buyer_2_name", "label": "2nd buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "2"}}, {"id": "buyer_2_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "2"}}, {"id": "buyer_2_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "2"}, {"field": "buyer_2_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_3_name", "label": "3rd buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "3"}}, {"id": "buyer_3_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "3"}}, {"id": "buyer_3_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "3"}, {"field": "buyer_3_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_4_name", "label": "4th buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "4"}}, {"id": "buyer_4_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "4"}}, {"id": "buyer_4_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "4"}, {"field": "buyer_4_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_5_name", "label": "5th buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "5"}}, {"id": "buyer_5_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "5"}}, {"id": "buyer_5_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "5"}, {"field": "buyer_5_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_6_name", "label": "6th buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "6"}}, {"id": "buyer_6_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "6"}}, {"id": "buyer_6_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "6"}, {"field": "buyer_6_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_7_name", "label": "7th buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "7"}}, {"id": "buyer_7_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "7"}}, {"id": "buyer_7_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "7"}, {"field": "buyer_7_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "buyer_8_name", "label": "8th buyer's name:", "type": "text", "required": true, "conditional": {"field": "buyer_count", "operator": "gte", "value": "8"}}, {"id": "buyer_8_entity_type", "label": "Is this buyer an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "buyer_count", "operator": "gte", "value": "8"}}, {"id": "buyer_8_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "buyer_count", "operator": "gte", "value": "8"}, {"field": "buyer_8_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "enter_buyer_address", "label": "Do you want to enter buyer's address now?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No, I'll add it later"}]}, {"id": "buyer_address", "label": "Buyer's Address", "type": "address", "required": true, "conditional": {"field": "enter_buyer_address", "operator": "equals", "value": "yes"}}]}, {"id": "seller_information", "title": "Seller Information", "fields": [{"id": "seller_count", "label": "How many sellers?", "type": "select", "required": true, "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}, {"value": "5", "label": "5"}, {"value": "6", "label": "6"}, {"value": "7", "label": "7"}, {"value": "8", "label": "8"}]}, {"id": "seller_1_name", "label": "<PERSON><PERSON>'s name:", "type": "text", "required": true}, {"id": "seller_1_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}]}, {"id": "seller_1_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"field": "seller_1_entity_type", "operator": "equals", "value": "entity"}}, {"id": "seller_2_name", "label": "2nd seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "2"}}, {"id": "seller_2_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "2"}}, {"id": "seller_2_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "2"}, {"field": "seller_2_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_3_name", "label": "3rd seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "3"}}, {"id": "seller_3_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "3"}}, {"id": "seller_3_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "3"}, {"field": "seller_3_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_4_name", "label": "4th seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "4"}}, {"id": "seller_4_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "4"}}, {"id": "seller_4_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "4"}, {"field": "seller_4_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_5_name", "label": "5th seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "5"}}, {"id": "seller_5_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "5"}}, {"id": "seller_5_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "5"}, {"field": "seller_5_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_6_name", "label": "6th seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "6"}}, {"id": "seller_6_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "6"}}, {"id": "seller_6_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "6"}, {"field": "seller_6_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_7_name", "label": "7th seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "7"}}, {"id": "seller_7_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "7"}}, {"id": "seller_7_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "7"}, {"field": "seller_7_entity_type", "operator": "equals", "value": "entity"}]}}, {"id": "seller_8_name", "label": "8th seller's name:", "type": "text", "required": true, "conditional": {"field": "seller_count", "operator": "gte", "value": "8"}}, {"id": "seller_8_entity_type", "label": "Is this seller an individual or entity?", "type": "radio", "required": true, "options": [{"value": "individual", "label": "Individual"}, {"value": "entity", "label": "Entity (LLC, Corporation, Trust, etc.)"}], "conditional": {"field": "seller_count", "operator": "gte", "value": "8"}}, {"id": "seller_8_entity_name", "label": "Entity name:", "type": "text", "required": true, "conditional": {"operator": "and", "conditions": [{"field": "seller_count", "operator": "gte", "value": "8"}, {"field": "seller_8_entity_type", "operator": "equals", "value": "entity"}]}}]}, {"id": "property_details", "title": "Property Details", "fields": [{"id": "property_address", "label": "Property Address", "type": "address", "required": true}, {"id": "legal_description", "label": "Legal Description (if known):", "type": "textarea", "required": false, "helpText": "This can usually be found on the deed or property tax records"}, {"id": "parcel_number", "label": "Tax Parcel Number:", "type": "text", "required": false}]}, {"id": "purchase_price", "title": "Purchase Price and Earnest Money", "fields": [{"id": "purchase_price", "label": "Purchase Price:", "type": "currency", "required": true}, {"id": "earnest_money_amount", "label": "Earnest Money Deposit:", "type": "currency", "required": true}, {"id": "earnest_money_form", "label": "Form of earnest money:", "type": "radio", "required": true, "options": [{"value": "check", "label": "Check"}, {"value": "wire", "label": "Wire Transfer"}, {"value": "cash", "label": "Cash"}, {"value": "other", "label": "Other"}]}, {"id": "earnest_money_other", "label": "Please specify:", "type": "text", "required": true, "conditional": {"field": "earnest_money_form", "operator": "equals", "value": "other"}}, {"id": "earnest_money_days", "label": "Days after effective date to deliver earnest money:", "type": "number", "required": true, "min": 0, "max": 30, "defaultValue": 3}]}, {"id": "financing", "title": "Buyer Financing", "fields": [{"id": "financing_type", "label": "How will the buyer pay for the property?", "type": "radio", "required": true, "options": [{"value": "cash", "label": "Cash"}, {"value": "conventional", "label": "Conventional Loan"}, {"value": "fha", "label": "FHA Loan"}, {"value": "va", "label": "VA Loan"}, {"value": "usda", "label": "USDA Loan"}, {"value": "seller_financing", "label": "Seller Financing"}, {"value": "assumption", "label": "<PERSON>an <PERSON>"}, {"value": "other", "label": "Other"}]}, {"id": "financing_other", "label": "Please describe the financing:", "type": "text", "required": true, "conditional": {"field": "financing_type", "operator": "equals", "value": "other"}}, {"id": "loan_amount", "label": "Loan Amount:", "type": "currency", "required": true, "conditional": {"field": "financing_type", "operator": "not_in", "value": ["cash"]}}, {"id": "down_payment", "label": "Down Payment:", "type": "currency", "required": true, "conditional": {"field": "financing_type", "operator": "not_in", "value": ["cash"]}}, {"id": "financing_contingency", "label": "Is this purchase contingent on buyer obtaining financing?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "financing_type", "operator": "not_equals", "value": "cash"}}, {"id": "financing_days", "label": "Days to obtain financing commitment:", "type": "number", "required": true, "min": 1, "max": 90, "defaultValue": 30, "conditional": {"field": "financing_contingency", "operator": "equals", "value": "yes"}}]}, {"id": "closing", "title": "Closing Details", "fields": [{"id": "closing_date", "label": "Closing Date:", "type": "date", "required": true}, {"id": "possession_date", "label": "When will buyer take possession?", "type": "radio", "required": true, "options": [{"value": "at_closing", "label": "At Closing"}, {"value": "before_closing", "label": "Before Closing"}, {"value": "after_closing", "label": "After Closing"}]}, {"id": "possession_date_specific", "label": "Possession Date:", "type": "date", "required": true, "conditional": {"field": "possession_date", "operator": "not_equals", "value": "at_closing"}}, {"id": "closing_costs_buyer", "label": "Buyer will pay the following closing costs:", "type": "checkbox", "required": false, "options": [{"value": "loan_origination", "label": "Loan Origination Fees"}, {"value": "appraisal", "label": "Appraisal Fee"}, {"value": "credit_report", "label": "Credit Report"}, {"value": "inspection", "label": "Inspection Fees"}, {"value": "attorney_buyer", "label": "Buyer's Attorney <PERSON><PERSON>"}, {"value": "title_insurance_owner", "label": "Owner's Title Insurance"}, {"value": "title_insurance_lender", "label": "Lender's Title Insurance"}, {"value": "recording_deed", "label": "Recording Fees for <PERSON><PERSON>"}, {"value": "survey", "label": "Survey"}, {"value": "other_buyer", "label": "Other"}]}, {"id": "closing_costs_seller", "label": "Seller will pay the following closing costs:", "type": "checkbox", "required": false, "options": [{"value": "real_estate_commission", "label": "Real Estate Commission"}, {"value": "attorney_seller", "label": "Seller's Attorney <PERSON><PERSON>"}, {"value": "title_search", "label": "Title Search"}, {"value": "transfer_tax", "label": "Transfer Tax/Stamps"}, {"value": "recording_satisfaction", "label": "Recording Fees for Mortgage Satisfaction"}, {"value": "home_warranty", "label": "Home Warranty"}, {"value": "other_seller", "label": "Other"}]}]}, {"id": "inspections", "title": "Inspections and Due Diligence", "fields": [{"id": "inspection_contingency", "label": "Is this purchase contingent on satisfactory inspections?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}]}, {"id": "inspection_period", "label": "Inspection period (days):", "type": "number", "required": true, "min": 1, "max": 30, "defaultValue": 10, "conditional": {"field": "inspection_contingency", "operator": "equals", "value": "yes"}}, {"id": "inspection_types", "label": "Types of inspections buyer may conduct:", "type": "checkbox", "required": false, "options": [{"value": "general", "label": "General Home Inspection"}, {"value": "pest", "label": "Pest/Termite Inspection"}, {"value": "radon", "label": "<PERSON><PERSON>"}, {"value": "lead", "label": "Lead-Based Paint"}, {"value": "mold", "label": "Mold Inspection"}, {"value": "roof", "label": "Roof Inspection"}, {"value": "pool", "label": "Pool/Spa Inspection"}, {"value": "septic", "label": "Septic Inspection"}, {"value": "well", "label": "Well Water Test"}, {"value": "structural", "label": "Structural/Engineering"}, {"value": "environmental", "label": "Environmental"}, {"value": "other_inspection", "label": "Other"}], "conditional": {"field": "inspection_contingency", "operator": "equals", "value": "yes"}}]}, {"id": "disclosures", "title": "Disclosures", "fields": [{"id": "lead_paint_disclosure", "label": "Was the property built before 1978?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "lead_paint_acknowledged", "label": "Buyer acknowledges receipt of lead-based paint disclosure", "type": "checkbox", "required": true, "options": [{"value": "acknowledged", "label": "I acknowledge receipt of the Lead-Based Paint Disclosure"}], "conditional": {"field": "lead_paint_disclosure", "operator": "equals", "value": "yes"}}, {"id": "hoa_disclosure", "label": "Is the property part of a Homeowners Association (HOA)?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "hoa_docs_received", "label": "Has buyer received HOA documents?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No, will be provided"}], "conditional": {"field": "hoa_disclosure", "operator": "equals", "value": "yes"}}]}, {"id": "additional_terms", "title": "Additional Terms and Conditions", "fields": [{"id": "additional_terms", "label": "Additional terms and conditions:", "type": "textarea", "required": false, "rows": 5}, {"id": "personal_property_included", "label": "Is any personal property included in the sale?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}]}, {"id": "personal_property_list", "label": "List all personal property included:", "type": "textarea", "required": true, "rows": 3, "conditional": {"field": "personal_property_included", "operator": "equals", "value": "yes"}}, {"id": "personal_property_excluded", "label": "List any items specifically excluded from the sale:", "type": "textarea", "required": false, "rows": 3}]}, {"id": "addendums", "title": "Addendums and Attachments", "fields": [{"id": "has_addendums", "label": "Are there any addendums or attachments to this agreement?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}]}, {"id": "attachment_1_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_2", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_2_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_2", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_3", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_3_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_3", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_4", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_4_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_4", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_5", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_5_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_5", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_6", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_6_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_6", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_7", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_7_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_7", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_8", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_8_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_8", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_9", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_9_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_9", "operator": "equals", "value": "yes"}}, {"id": "add_attachment_10", "label": "Add another attachment?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}], "conditional": {"field": "has_addendums", "operator": "equals", "value": "yes"}}, {"id": "attachment_10_name", "label": "Attachment name/description:", "type": "text", "required": true, "conditional": {"field": "add_attachment_10", "operator": "equals", "value": "yes"}}]}, {"id": "real_estate_agents", "title": "Real Estate Professionals", "fields": [{"id": "buyer_has_agent", "label": "Is the buyer represented by a real estate agent?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}]}, {"id": "buyer_agent_name", "label": "Buyer's Agent Name:", "type": "text", "required": true, "conditional": {"field": "buyer_has_agent", "operator": "equals", "value": "yes"}}, {"id": "buyer_brokerage", "label": "Buyer's Brokerage:", "type": "text", "required": true, "conditional": {"field": "buyer_has_agent", "operator": "equals", "value": "yes"}}, {"id": "seller_has_agent", "label": "Is the seller represented by a real estate agent?", "type": "radio", "required": true, "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}]}, {"id": "seller_agent_name", "label": "<PERSON><PERSON>'s Agent Name:", "type": "text", "required": true, "conditional": {"field": "seller_has_agent", "operator": "equals", "value": "yes"}}, {"id": "seller_brokerage", "label": "<PERSON><PERSON>'s Brokerage:", "type": "text", "required": true, "conditional": {"field": "seller_has_agent", "operator": "equals", "value": "yes"}}]}, {"id": "agreement_execution", "title": "Agreement Execution", "fields": [{"id": "offer_expiration_date", "label": "This offer expires on:", "type": "date", "required": true}, {"id": "offer_expiration_time", "label": "At time:", "type": "text", "required": true, "placeholder": "5:00 PM"}, {"id": "effective_date", "label": "Effective Date (when all parties have signed):", "type": "date", "required": false, "helpText": "Leave blank until all parties have signed"}]}, {"id": "state_disclosures", "title": "WV State-Specific Disclosures", "fields": [{"id": "wv_disclosure_acknowledgment", "label": "I acknowledge that I have received all required WV state disclosures", "type": "checkbox", "required": true, "options": [{"value": "acknowledged", "label": "I acknowledge receipt of all required state disclosures"}]}]}], "fieldTypes": {"text": {"component": "TextInput", "validation": ["required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "pattern"]}, "textarea": {"component": "TextArea", "validation": ["required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>"]}, "radio": {"component": "RadioGroup", "validation": ["required"]}, "checkbox": {"component": "CheckboxGroup", "validation": ["required", "minItems", "maxItems"]}, "select": {"component": "Select", "validation": ["required"]}, "date": {"component": "DatePicker", "validation": ["required", "minDate", "maxDate"]}, "currency": {"component": "CurrencyInput", "validation": ["required", "min", "max"]}, "number": {"component": "NumberInput", "validation": ["required", "min", "max"]}, "address": {"component": "AddressInput", "validation": ["required"], "fields": {"street": {"label": "Street Address", "required": true}, "street2": {"label": "Address Line 2", "required": false}, "city": {"label": "City", "required": true}, "state": {"label": "State", "required": true, "type": "select"}, "zipcode": {"label": "ZIP Code", "required": true}}}, "info": {"component": "InfoDisplay", "validation": []}}, "conditionalOperators": {"equals": "Value equals", "not_equals": "Value does not equal", "in": "Value is in list", "not_in": "Value is not in list", "gte": "Value is greater than or equal to", "lte": "Value is less than or equal to", "gt": "Value is greater than", "lt": "Value is less than"}, "validation": {"required": "This field is required", "minLength": "Minimum length is {min}", "maxLength": "Maximum length is {max}", "pattern": "Invalid format", "min": "Value must be at least {min}", "max": "Value must be no more than {max}", "minDate": "Date must be after {min}", "maxDate": "Date must be before {max}", "minItems": "Select at least {min} items", "maxItems": "Select no more than {max} items"}}