{"title": "US State Purchase Agreement Forms", "description": "Comprehensive residential purchase and sale agreement forms for all 50 US states", "version": "1.0.0", "generatedAt": "2025-06-26T18:35:54.462Z", "totalStates": 50, "states": [{"name": "Alabama", "abbr": "AL", "formId": "al_purchase_agreement", "formFile": "AL-Purchase-Agreement.json", "apiEndpoint": "/api/forms/al", "routePath": "/forms/al"}, {"name": "Alaska", "abbr": "AK", "formId": "ak_purchase_agreement", "formFile": "AK-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ak", "routePath": "/forms/ak"}, {"name": "Arizona", "abbr": "AZ", "formId": "az_purchase_agreement", "formFile": "AZ-Purchase-Agreement.json", "apiEndpoint": "/api/forms/az", "routePath": "/forms/az"}, {"name": "Arkansas", "abbr": "AR", "formId": "ar_purchase_agreement", "formFile": "AR-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ar", "routePath": "/forms/ar"}, {"name": "California", "abbr": "CA", "formId": "ca_purchase_agreement", "formFile": "CA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ca", "routePath": "/forms/ca"}, {"name": "Colorado", "abbr": "CO", "formId": "co_purchase_agreement", "formFile": "CO-Purchase-Agreement.json", "apiEndpoint": "/api/forms/co", "routePath": "/forms/co"}, {"name": "Connecticut", "abbr": "CT", "formId": "ct_purchase_agreement", "formFile": "CT-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ct", "routePath": "/forms/ct"}, {"name": "Delaware", "abbr": "DE", "formId": "de_purchase_agreement", "formFile": "DE-Purchase-Agreement.json", "apiEndpoint": "/api/forms/de", "routePath": "/forms/de"}, {"name": "Florida", "abbr": "FL", "formId": "fl_purchase_agreement", "formFile": "FL-Purchase-Agreement.json", "apiEndpoint": "/api/forms/fl", "routePath": "/forms/fl"}, {"name": "Georgia", "abbr": "GA", "formId": "ga_purchase_agreement", "formFile": "GA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ga", "routePath": "/forms/ga"}, {"name": "Hawaii", "abbr": "HI", "formId": "hi_purchase_agreement", "formFile": "HI-Purchase-Agreement.json", "apiEndpoint": "/api/forms/hi", "routePath": "/forms/hi"}, {"name": "Idaho", "abbr": "ID", "formId": "id_purchase_agreement", "formFile": "ID-Purchase-Agreement.json", "apiEndpoint": "/api/forms/id", "routePath": "/forms/id"}, {"name": "Illinois", "abbr": "IL", "formId": "il_purchase_agreement", "formFile": "IL-Purchase-Agreement.json", "apiEndpoint": "/api/forms/il", "routePath": "/forms/il"}, {"name": "Indiana", "abbr": "IN", "formId": "in_purchase_agreement", "formFile": "IN-Purchase-Agreement.json", "apiEndpoint": "/api/forms/in", "routePath": "/forms/in"}, {"name": "Iowa", "abbr": "IA", "formId": "ia_purchase_agreement", "formFile": "IA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ia", "routePath": "/forms/ia"}, {"name": "Kansas", "abbr": "KS", "formId": "ks_purchase_agreement", "formFile": "KS-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ks", "routePath": "/forms/ks"}, {"name": "Kentucky", "abbr": "KY", "formId": "ky_purchase_agreement", "formFile": "KY-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ky", "routePath": "/forms/ky"}, {"name": "Louisiana", "abbr": "LA", "formId": "la_purchase_agreement", "formFile": "LA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/la", "routePath": "/forms/la"}, {"name": "Maine", "abbr": "ME", "formId": "me_purchase_agreement", "formFile": "ME-Purchase-Agreement.json", "apiEndpoint": "/api/forms/me", "routePath": "/forms/me"}, {"name": "Maryland", "abbr": "MD", "formId": "md_purchase_agreement", "formFile": "MD-Purchase-Agreement.json", "apiEndpoint": "/api/forms/md", "routePath": "/forms/md"}, {"name": "Massachusetts", "abbr": "MA", "formId": "ma_purchase_agreement", "formFile": "MA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ma", "routePath": "/forms/ma"}, {"name": "Michigan", "abbr": "MI", "formId": "mi_purchase_agreement", "formFile": "MI-Purchase-Agreement.json", "apiEndpoint": "/api/forms/mi", "routePath": "/forms/mi"}, {"name": "Minnesota", "abbr": "MN", "formId": "mn_purchase_agreement", "formFile": "MN-Purchase-Agreement.json", "apiEndpoint": "/api/forms/mn", "routePath": "/forms/mn"}, {"name": "Mississippi", "abbr": "MS", "formId": "ms_purchase_agreement", "formFile": "MS-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ms", "routePath": "/forms/ms"}, {"name": "Missouri", "abbr": "MO", "formId": "mo_purchase_agreement", "formFile": "MO-Purchase-Agreement.json", "apiEndpoint": "/api/forms/mo", "routePath": "/forms/mo"}, {"name": "Montana", "abbr": "MT", "formId": "mt_purchase_agreement", "formFile": "MT-Purchase-Agreement.json", "apiEndpoint": "/api/forms/mt", "routePath": "/forms/mt"}, {"name": "Nebraska", "abbr": "NE", "formId": "ne_purchase_agreement", "formFile": "NE-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ne", "routePath": "/forms/ne"}, {"name": "Nevada", "abbr": "NV", "formId": "nv_purchase_agreement", "formFile": "NV-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nv", "routePath": "/forms/nv"}, {"name": "New Hampshire", "abbr": "NH", "formId": "nh_purchase_agreement", "formFile": "NH-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nh", "routePath": "/forms/nh"}, {"name": "New Jersey", "abbr": "NJ", "formId": "nj_purchase_agreement", "formFile": "NJ-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nj", "routePath": "/forms/nj"}, {"name": "New Mexico", "abbr": "NM", "formId": "nm_purchase_agreement", "formFile": "NM-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nm", "routePath": "/forms/nm"}, {"name": "New York", "abbr": "NY", "formId": "ny_purchase_agreement", "formFile": "NY-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ny", "routePath": "/forms/ny"}, {"name": "North Carolina", "abbr": "NC", "formId": "nc_purchase_agreement", "formFile": "NC-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nc", "routePath": "/forms/nc"}, {"name": "North Dakota", "abbr": "ND", "formId": "nd_purchase_agreement", "formFile": "ND-Purchase-Agreement.json", "apiEndpoint": "/api/forms/nd", "routePath": "/forms/nd"}, {"name": "Ohio", "abbr": "OH", "formId": "oh_purchase_agreement", "formFile": "OH-Purchase-Agreement.json", "apiEndpoint": "/api/forms/oh", "routePath": "/forms/oh"}, {"name": "Oklahoma", "abbr": "OK", "formId": "ok_purchase_agreement", "formFile": "OK-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ok", "routePath": "/forms/ok"}, {"name": "Oregon", "abbr": "OR", "formId": "or_purchase_agreement", "formFile": "OR-Purchase-Agreement.json", "apiEndpoint": "/api/forms/or", "routePath": "/forms/or"}, {"name": "Pennsylvania", "abbr": "PA", "formId": "pa_purchase_agreement", "formFile": "PA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/pa", "routePath": "/forms/pa"}, {"name": "Rhode Island", "abbr": "RI", "formId": "ri_purchase_agreement", "formFile": "RI-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ri", "routePath": "/forms/ri"}, {"name": "South Carolina", "abbr": "SC", "formId": "sc_purchase_agreement", "formFile": "SC-Purchase-Agreement.json", "apiEndpoint": "/api/forms/sc", "routePath": "/forms/sc"}, {"name": "South Dakota", "abbr": "SD", "formId": "sd_purchase_agreement", "formFile": "SD-Purchase-Agreement.json", "apiEndpoint": "/api/forms/sd", "routePath": "/forms/sd"}, {"name": "Tennessee", "abbr": "TN", "formId": "tn_purchase_agreement", "formFile": "TN-Purchase-Agreement.json", "apiEndpoint": "/api/forms/tn", "routePath": "/forms/tn"}, {"name": "Texas", "abbr": "TX", "formId": "tx_purchase_agreement", "formFile": "TX-Purchase-Agreement.json", "apiEndpoint": "/api/forms/tx", "routePath": "/forms/tx"}, {"name": "Utah", "abbr": "UT", "formId": "ut_purchase_agreement", "formFile": "UT-Purchase-Agreement.json", "apiEndpoint": "/api/forms/ut", "routePath": "/forms/ut"}, {"name": "Vermont", "abbr": "VT", "formId": "vt_purchase_agreement", "formFile": "VT-Purchase-Agreement.json", "apiEndpoint": "/api/forms/vt", "routePath": "/forms/vt"}, {"name": "Virginia", "abbr": "VA", "formId": "va_purchase_agreement", "formFile": "VA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/va", "routePath": "/forms/va"}, {"name": "Washington", "abbr": "WA", "formId": "wa_purchase_agreement", "formFile": "WA-Purchase-Agreement.json", "apiEndpoint": "/api/forms/wa", "routePath": "/forms/wa"}, {"name": "West Virginia", "abbr": "WV", "formId": "wv_purchase_agreement", "formFile": "WV-Purchase-Agreement.json", "apiEndpoint": "/api/forms/wv", "routePath": "/forms/wv"}, {"name": "Wisconsin", "abbr": "WI", "formId": "wi_purchase_agreement", "formFile": "WI-Purchase-Agreement.json", "apiEndpoint": "/api/forms/wi", "routePath": "/forms/wi"}, {"name": "Wyoming", "abbr": "WY", "formId": "wy_purchase_agreement", "formFile": "WY-Purchase-Agreement.json", "apiEndpoint": "/api/forms/wy", "routePath": "/forms/wy"}], "fieldTypes": {"text": {"component": "TextInput", "validation": ["required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "pattern"]}, "textarea": {"component": "TextArea", "validation": ["required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>"]}, "radio": {"component": "RadioGroup", "validation": ["required"]}, "checkbox": {"component": "CheckboxGroup", "validation": ["required", "minItems", "maxItems"]}, "select": {"component": "Select", "validation": ["required"]}, "date": {"component": "DatePicker", "validation": ["required", "minDate", "maxDate"]}, "currency": {"component": "CurrencyInput", "validation": ["required", "min", "max"]}, "number": {"component": "NumberInput", "validation": ["required", "min", "max"]}, "address": {"component": "AddressInput", "validation": ["required"]}, "info": {"component": "InfoDisplay", "validation": []}}, "commonSections": ["property_type", "buyer_information", "seller_information", "property_details", "purchase_price", "financing", "closing", "inspections", "disclosures", "additional_terms", "addendums", "real_estate_agents", "agreement_execution"]}