"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowRight, FileText, MapPin, Zap, Shield, CheckCircle, Clock, Building, Play, DollarSign, PieChart, TrendingUp, BarChart3, Menu, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

export default function OriginalHome() {
  const [isAssignable, setIsAssignable] = useState(false);
  const [buyerType, setBuyerType] = useState("");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const howItWorksSteps = [
    {
      step: "1",
      title: "Type address",
      description: "Smart autocomplete finds your property instantly as you type",
      icon: MapPin,
    },
    {
      step: "2", 
      title: "Select from autocomplete",
      description: "Choose the correct property from verified suggestions",
      icon: CheckCircle,
    },
    {
      step: "3",
      title: "Get pre-filled purchase agreement",
      description: "Owner name, legal description, assessed value auto-populate",
      icon: FileText,
    },
    {
      step: "4",
      title: "Download complete package",
      description: "All required supporting documents included automatically",
      icon: Zap,
    },
  ];

  const keyBenefits = [
    {
      icon: Shield,
      title: "50-State Compliance",
      description: "Official state documents that meet legal requirements in all 50 states",
      highlight: "Legally compliant everywhere.",
    },
    {
      icon: Clock,
      title: "90-Second Generation",
      description: "From property address to complete contract package in under 90 seconds",
      highlight: "Lightning-fast results.",
    },
    {
      icon: CheckCircle,
      title: "Official State Forms",
      description: "Every template uses official state-approved documents for accuracy and compliance",
      highlight: "Professional-grade quality.",
    },
    {
      icon: Zap,
      title: "Complete Automation",
      description: "Property data auto-populates all fields - no manual entry required",
      highlight: "Zero manual errors.",
    },
  ];

  const technicalFeatures = [
    "50-state legal compliance engine with official state documents",
    "Real-time property database integration for instant auto-population",
    "Advanced address validation and standardization system",
    "State-specific jurisdiction compliance rules and requirements",
    "Automated legal description and owner verification",
    "Professional-grade document generation in under 90 seconds",
  ];

  return (
    <>


      {/* Hero Section with Address Input */}
      <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 animate-pulse" />
          <div className="absolute top-0 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        </div>

        <div className="container mx-auto px-4 py-20 lg:py-32 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white leading-tight mb-6">
              Create Professional Real Estate<br />
              <span className="text-2xl sm:text-3xl lg:text-4xl text-gray-300">Purchase Agreements</span>
            </h1>

            <p className="text-base sm:text-lg text-gray-300 mb-12 max-w-2xl mx-auto">
              From Property Research to Signed Contracts.<br />No Realtors. No Hassle.
            </p>
            
            {/* Address Input with Autocomplete Preview */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-white rounded-lg shadow-2xl p-2">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-gray-400 ml-3" />
                  <input
                    type="text"
                    placeholder="Enter property address..."
                    className="flex-1 px-3 py-4 text-lg focus:outline-none"
                  />
                  <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-md">
                    Generate Agreement
                  </Button>
                </div>
                
                {/* Additional Parameters */}
                <div className="flex items-center gap-6 pt-4 border-t border-gray-100">
                  <div className="flex items-center gap-3">
                    <Switch
                      id="assignable"
                      checked={isAssignable}
                      onCheckedChange={setIsAssignable}
                    />
                    <Label htmlFor="assignable" className="text-sm font-medium cursor-pointer">
                      Assignable
                    </Label>
                  </div>
                  
                  <div className="flex-1">
                    <Select value={buyerType} onValueChange={setBuyerType}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select buyer type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="wholesalers">Wholesalers</SelectItem>
                        <SelectItem value="section8">Section 8 Investors</SelectItem>
                        <SelectItem value="direct">Direct Buyers</SelectItem>
                        <SelectItem value="na">N/A</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

            </div>

            {/* Key Features */}
            <div className="mt-12 max-w-2xl mx-auto">
              <ul className="space-y-3 text-gray-300">
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                  <span>50-state compliant legal forms</span>
                </li>
                <li className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-blue-400 flex-shrink-0" />
                  <span>90-second document generation</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-purple-400 flex-shrink-0" />
                  <span>Official state documents</span>
                </li>
                <li className="flex items-center gap-3">
                  <Zap className="h-5 w-5 text-yellow-400 flex-shrink-0" />
                  <span>Automated property data population + seller disclosure forms</span>
                </li>
              </ul>
            </div>

            {/* Moving Carousel - Serving 50 States */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold text-white mb-8 text-center">Serving 50 States</h3>
              <div className="relative overflow-hidden">
                <div className="flex gap-6 animate-scroll">
                  {/* First set of cards */}
                  <div className="flex gap-6 flex-shrink-0">
                    {/* Wholesalers Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">🏠</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Wholesalers</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Instant property analysis</li>
                        <li>• Automated owner research</li>
                        <li>• Quick contract generation</li>
                      </ul>
                    </div>
                    {/* Section 8 Investors Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">🏢</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Section 8 Investors</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Scale your portfolio with streamlined acquisitions</li>
                        <li>• Multi-property analysis</li>
                        <li>• Portfolio tracking</li>
                      </ul>
                    </div>
                    {/* Direct Buyers Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">💼</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Direct Buyers</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Professional documentation that closes deals</li>
                        <li>• Legal compliance</li>
                        <li>• Custom contract terms</li>
                      </ul>
                    </div>
                  </div>
                  {/* Duplicate set for continuous scroll */}
                  <div className="flex gap-6 flex-shrink-0">
                    {/* Wholesalers Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">🏠</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Wholesalers</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Instant property analysis</li>
                        <li>• Automated owner research</li>
                        <li>• Quick contract generation</li>
                      </ul>
                    </div>
                    {/* Section 8 Investors Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">🏢</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Section 8 Investors</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Scale your portfolio with streamlined acquisitions</li>
                        <li>• Multi-property analysis</li>
                        <li>• Portfolio tracking</li>
                      </ul>
                    </div>
                    {/* Direct Buyers Card */}
                    <div className="w-80 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 flex-shrink-0">
                      <div className="text-3xl mb-3">💼</div>
                      <h4 className="text-xl font-semibold text-white mb-3">Direct Buyers</h4>
                      <ul className="space-y-2 text-gray-300">
                        <li>• Professional documentation that closes deals</li>
                        <li>• Legal compliance</li>
                        <li>• Custom contract terms</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Experience Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                One search. Complete intelligence.
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Turn property addresses into complete, accurate contract packages instantly.
              </p>
            </div>

            {/* Interactive Demo */}
            <div className="bg-gray-50 rounded-2xl p-8 mb-12">
              <div className="max-w-2xl mx-auto">
                <div className="bg-white rounded-lg shadow-lg p-6 mb-4">
                  <div className="flex items-center gap-3 mb-4">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <div className="flex-1">
                      <div className="text-lg font-medium">742 Evergreen Terrace, Springfield</div>
                      <div className="text-sm text-gray-500">Typing...</div>
                    </div>
                  </div>
                  {/* Autocomplete Dropdown */}
                  <div className="border-t pt-4 space-y-2">
                    <div className="p-3 hover:bg-gray-50 rounded cursor-pointer">
                      <div className="font-medium">742 Evergreen Terrace</div>
                      <div className="text-sm text-gray-600">Springfield, IL 62701</div>
                    </div>
                    <div className="p-3 hover:bg-gray-50 rounded cursor-pointer">
                      <div className="font-medium">742 Evergreen Ave</div>
                      <div className="text-sm text-gray-600">Springfield, MO 65804</div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse" />
                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse delay-100" />
                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse delay-200" />
                </div>

                {/* Auto-populated Results */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="font-semibold text-lg mb-4">Auto-Populated Fields:</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Owner Name:</span>
                      <span className="ml-2 font-medium">Homer J. Simpson</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Legal Description:</span>
                      <span className="ml-2 font-medium">Lot 12, Block 5...</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Assessed Value:</span>
                      <span className="ml-2 font-medium">$289,000</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Parcel ID:</span>
                      <span className="ml-2 font-medium">14-28-126-005</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Grid */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Smart Autocomplete</h3>
                <p className="text-gray-600">Address suggestions appear instantly as you type</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Instant Generation</h3>
                <p className="text-gray-600">All required docs automatically appear</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Auto-Population</h3>
                <p className="text-gray-600">Property details pre-fill contract fields</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                How it works
              </h2>
              <p className="text-xl text-gray-600">
                From address to agreement in 4 simple steps
              </p>
            </div>

            <div className="space-y-8">
              {howItWorksSteps.map((step, index) => (
                <div key={index} className="flex gap-6 items-start">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">{step.step}</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                  <div className="hidden md:block">
                    <step.icon className="h-8 w-8 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Professional. Compliant. Fast.
              </h2>
              <p className="text-xl text-gray-600">
                50-state compliant legal forms generated in 90 seconds
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {keyBenefits.map((benefit, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-50 to-white border border-gray-100 rounded-2xl p-8 hover:shadow-lg transition-shadow">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <benefit.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                      <p className="text-gray-600 mb-3">{benefit.description}</p>
                      <p className="text-sm font-medium text-purple-600">{benefit.highlight}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technical Intelligence Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
                Official documents. State-compliant. Automated.
              </h2>
              <p className="text-xl text-gray-300">
                Professional-grade legal technology serving all 50 states
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {technicalFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">{feature}</span>
                </div>
              ))}
            </div>

            <div className="mt-12 bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <div className="grid grid-cols-3 gap-8 text-center">
                <div>
                  <div className="text-3xl font-bold mb-2">50</div>
                  <div className="text-gray-400">States covered</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">90s</div>
                  <div className="text-gray-400">Average generation time</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">99.9%</div>
                  <div className="text-gray-400">Accuracy rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            Ready to transform your workflow?
          </h2>
          <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
            Start generating purchase agreements instantly
          </p>
          <div className="max-w-md mx-auto">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2">
              <div className="flex items-center gap-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/60 focus:outline-none focus:bg-white/20"
                />
                <Button className="bg-white text-purple-600 hover:bg-gray-100 px-6 py-3 font-semibold">
                  Get Started Free
                </Button>
              </div>
            </div>
            <p className="text-sm text-purple-200 mt-4">
              No credit card required • Start in 60 seconds
            </p>
          </div>
        </div>
      </section>
    </>
  );
}
