# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
node_modules/*
node_modules/
app/api
app/contact
forgot-password
help
learna
login
pricing
signup
.next
.history

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Additional ignores
un
app/contact
forgot-password
help
learna
login
pricing
signup
node_modules
\
\
\
https://github.com/brycebayens/landing-staging.git
