// Form utility functions for conditional logic and validation

export interface ConditionalLogic {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  logic?: 'AND' | 'OR';
  conditions?: ConditionalLogic[];
}

/**
 * Evaluates a conditional logic expression against form values
 */
export function evaluateCondition(
  condition: ConditionalLogic,
  values: Record<string, any>
): boolean {
  if (!condition) return true;

  const fieldValue = values[condition.field];
  let result = false;

  switch (condition.operator) {
    case 'equals':
      result = fieldValue === condition.value;
      break;
    case 'not_equals':
      result = fieldValue !== condition.value;
      break;
    case 'contains':
      result = String(fieldValue || '').toLowerCase().includes(String(condition.value).toLowerCase());
      break;
    case 'greater_than':
      result = Number(fieldValue) > Number(condition.value);
      break;
    case 'less_than':
      result = Number(fieldValue) < Number(condition.value);
      break;
    case 'in':
      result = Array.isArray(condition.value) && condition.value.includes(fieldValue);
      break;
    case 'not_in':
      result = Array.isArray(condition.value) && !condition.value.includes(fieldValue);
      break;
    default:
      result = false;
  }

  // Handle nested conditions
  if (condition.conditions && condition.conditions.length > 0) {
    const nestedResults = condition.conditions.map(nestedCondition => 
      evaluateCondition(nestedCondition, values)
    );

    if (condition.logic === 'OR') {
      result = result || nestedResults.some(r => r);
    } else {
      // Default to AND
      result = result && nestedResults.every(r => r);
    }
  }

  return result;
}

/**
 * Validates a form field value
 */
export function validateField(field: any, value: any): string | null {
  if (!field) return null;

  // Required validation
  if (field.required && (value === null || value === undefined || value === '')) {
    return `${field.label} is required`;
  }

  // Skip other validations if field is empty and not required
  if (!value && !field.required) return null;

  // String length validations
  if (typeof value === 'string') {
    if (field.minLength && value.length < field.minLength) {
      return `${field.label} must be at least ${field.minLength} characters`;
    }
    if (field.maxLength && value.length > field.maxLength) {
      return `${field.label} must be no more than ${field.maxLength} characters`;
    }
  }

  // Number validations
  if (field.type === 'number' || field.type === 'currency') {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      return `${field.label} must be a valid number`;
    }
    if (field.min !== undefined && numValue < field.min) {
      return `${field.label} must be at least ${field.min}`;
    }
    if (field.max !== undefined && numValue > field.max) {
      return `${field.label} must be no more than ${field.max}`;
    }
  }

  // Date validations
  if (field.type === 'date') {
    const dateValue = new Date(value);
    if (isNaN(dateValue.getTime())) {
      return `${field.label} must be a valid date`;
    }
    if (field.minDate && dateValue < new Date(field.minDate)) {
      return `${field.label} must be after ${field.minDate}`;
    }
    if (field.maxDate && dateValue > new Date(field.maxDate)) {
      return `${field.label} must be before ${field.maxDate}`;
    }
  }

  return null;
}

/**
 * Validates all fields in a form
 */
export function validateForm(
  formData: any,
  values: Record<string, any>
): Record<string, string> {
  const errors: Record<string, string> = {};

  if (!formData?.sections) return errors;

  formData.sections.forEach((section: any) => {
    section.fields.forEach((field: any) => {
      // Skip validation if field is not visible due to conditional logic
      if (field.conditional && !evaluateCondition(field.conditional, values)) {
        return;
      }

      const error = validateField(field, values[field.id]);
      if (error) {
        errors[field.id] = error;
      }
    });
  });

  return errors;
}

/**
 * Gets all visible fields based on conditional logic
 */
export function getVisibleFields(
  formData: any,
  values: Record<string, any>
): any[] {
  if (!formData?.sections) return [];

  const visibleFields: any[] = [];

  formData.sections.forEach((section: any) => {
    section.fields.forEach((field: any) => {
      if (!field.conditional || evaluateCondition(field.conditional, values)) {
        visibleFields.push(field);
      }
    });
  });

  return visibleFields;
}
