// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Address suggestion interface
export interface AddressSuggestion {
  id: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  full_address: string;
  latitude?: number;
  longitude?: number;
}

// Property detail interface
export interface PropertyDetail {
  address: string;
  city: string;
  state: string;
  zip: string;
  county?: string;
  parcel_number?: string;
  legal_description?: string;
  owner_name?: string;
  estimated_value?: number;
  tax_assessed_value?: number;
}

// Fetch address autocomplete suggestions
export const fetchAddressSuggestions = async (search: string): Promise<AddressSuggestion[]> => {
  if (!search || search.length < 3) {
    return [];
  }

  try {
    const response = await fetch('https://api.propbolt.com/v2/AutoComplete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_PROPBOLT_API_KEY || 'YOUR_API_KEY_HERE',
      },
      body: JSON.stringify({ search }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Transform the API response to match our interface
    const suggestions: AddressSuggestion[] = data.results?.map((result: any, index: number) => ({
      id: result.id || index.toString(),
      address: result.address || result.street_address || '',
      city: result.city || '',
      state: result.state || '',
      zip: result.zip || result.postal_code || '',
      full_address: result.full_address || `${result.address || result.street_address || ''}, ${result.city || ''}, ${result.state || ''} ${result.zip || result.postal_code || ''}`.trim(),
      latitude: result.latitude,
      longitude: result.longitude
    })) || [];

    return suggestions;
  } catch (error) {
    console.error('Address autocomplete error:', error);

    // Fallback to mock data on error
    const mockSuggestions: AddressSuggestion[] = [
      {
        id: "1",
        address: "742 Evergreen Terrace",
        city: "Springfield",
        state: "IL",
        zip: "62701",
        full_address: "742 Evergreen Terrace, Springfield, IL 62701"
      },
      {
        id: "2",
        address: "742 Evergreen Ave",
        city: "Springfield",
        state: "MO",
        zip: "65804",
        full_address: "742 Evergreen Ave, Springfield, MO 65804"
      }
    ];

    return mockSuggestions.filter(suggestion =>
      suggestion.full_address.toLowerCase().includes(search.toLowerCase())
    );
  }
};

// Fetch property details
export const fetchPropertyDetails = async (address: string): Promise<PropertyDetail | null> => {
  try {
    const response = await fetch('https://api.propbolt.com/v2/PropertyDetails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_PROPBOLT_API_KEY || 'YOUR_API_KEY_HERE',
      },
      body: JSON.stringify({ address }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Transform the API response to match our interface
    const propertyDetail: PropertyDetail = {
      address: data.address || address,
      city: data.city || '',
      state: data.state || '',
      zip: data.zip || data.postal_code || '',
      county: data.county,
      parcel_number: data.parcel_number || data.apn,
      legal_description: data.legal_description,
      owner_name: data.owner_name || data.owner,
      estimated_value: data.estimated_value || data.market_value,
      tax_assessed_value: data.tax_assessed_value || data.assessed_value
    };

    return propertyDetail;
  } catch (error) {
    console.error('Property details error:', error);

    // Return mock data for demo purposes
    return {
      address: address,
      city: "Orlando",
      state: "FL",
      zip: "32801",
      county: "Orange County",
      parcel_number: "14-28-126-005",
      legal_description: "LOT 12, BLOCK 5, EVERGREEN ESTATES",
      owner_name: "John & Mary Simpson",
      estimated_value: 289000,
      tax_assessed_value: 275000
    };
  }
};