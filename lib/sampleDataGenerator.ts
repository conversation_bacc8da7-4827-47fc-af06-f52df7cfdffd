interface SampleData {
  [key: string]: any;
}

const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
const streetNames = ['Main', 'Oak', 'Maple', 'Cedar', 'Pine', 'Elm', 'Washington', 'Park'];
const streetTypes = ['Street', 'Avenue', 'Boulevard', 'Drive', 'Lane', 'Court', 'Road', 'Way'];
const cities = ['Miami', 'Orlando', 'Tampa', 'Jacksonville', 'Fort Lauderdale', 'Tallahassee', 'Naples', 'Sarasota'];
const entityNames = ['Investment Group', 'Holdings', 'Properties', 'Real Estate Trust', 'Capital Partners'];

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function generateRandomDate(start: Date, end: Date): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString().split('T')[0];
}

function generatePhoneNumber(): string {
  const areaCode = Math.floor(Math.random() * 900) + 100;
  const prefix = Math.floor(Math.random() * 900) + 100;
  const lineNumber = Math.floor(Math.random() * 9000) + 1000;
  return `${areaCode}-${prefix}-${lineNumber}`;
}

function generateEmail(firstName: string, lastName: string): string {
  const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'email.com'];
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${getRandomElement(domains)}`;
}

export function generateSampleData(stateCode: string): SampleData {
  const buyerFirstName = getRandomElement(firstNames);
  const buyerLastName = getRandomElement(lastNames);
  const sellerFirstName = getRandomElement(firstNames);
  const sellerLastName = getRandomElement(lastNames);
  
  const purchasePrice = Math.floor(Math.random() * 700000) + 300000;
  const earnestMoney = Math.floor(purchasePrice * 0.01);
  const downPayment = Math.floor(purchasePrice * 0.2);
  
  const today = new Date();
  const closingDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
  const inspectionPeriod = new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000); // 10 days from now
  
  const streetNumber = Math.floor(Math.random() * 9000) + 1000;
  const zipCode = Math.floor(Math.random() * 90000) + 10000;
  
  return {
    // Property Information
    property_type: 'single_family',
    property_address: `${streetNumber} ${getRandomElement(streetNames)} ${getRandomElement(streetTypes)}`,
    property_city: getRandomElement(cities),
    property_state: stateCode,
    property_zip: zipCode.toString(),
    
    // Buyer Information
    buyer_count: '1',
    buyer_1_name: `${buyerFirstName} ${buyerLastName}`,
    buyer_1_entity_type: 'individual',
    buyer_1_phone: generatePhoneNumber(),
    buyer_1_email: generateEmail(buyerFirstName, buyerLastName),
    buyer_1_address: `${Math.floor(Math.random() * 900) + 100} ${getRandomElement(streetNames)} ${getRandomElement(streetTypes)}`,
    buyer_1_city: getRandomElement(cities),
    buyer_1_state: stateCode,
    buyer_1_zip: (zipCode + Math.floor(Math.random() * 100)).toString(),
    
    // Seller Information
    seller_count: '1',
    seller_1_name: `${sellerFirstName} ${sellerLastName}`,
    seller_1_entity_type: 'individual',
    seller_1_phone: generatePhoneNumber(),
    seller_1_email: generateEmail(sellerFirstName, sellerLastName),
    
    // Purchase Terms
    purchase_price: purchasePrice.toString(),
    earnest_money_amount: earnestMoney.toString(),
    earnest_money_days: '3',
    financing_type: 'conventional',
    down_payment_amount: downPayment.toString(),
    loan_amount: (purchasePrice - downPayment).toString(),
    
    // Important Dates
    effective_date: today.toISOString().split('T')[0],
    closing_date: closingDate.toISOString().split('T')[0],
    inspection_period_days: '10',
    inspection_period_end: inspectionPeriod.toISOString().split('T')[0],
    
    // Contingencies
    financing_contingency: 'yes',
    financing_contingency_days: '21',
    inspection_contingency: 'yes',
    appraisal_contingency: 'yes',
    sale_of_buyer_property: 'no',
    
    // Closing Costs
    title_company: `${getRandomElement(['First', 'Premier', 'National', 'Liberty'])} Title Company`,
    closing_costs_paid_by: 'split',
    
    // Additional Terms
    home_warranty: 'yes',
    home_warranty_cost: '500',
    personal_property_included: 'Refrigerator, Washer, Dryer',
    
    // HOA Information (for condos)
    hoa_dues: '250',
    hoa_dues_frequency: 'monthly',
    hoa_special_assessment: 'no',
    
    // Disclosures
    lead_paint_disclosure: 'no',
    property_disclosure_provided: 'yes',
    
    // Additional standard fields
    possession_date: closingDate.toISOString().split('T')[0],
    prorations: 'yes',
    survey_required: 'yes',
    survey_paid_by: 'buyer',
    termite_inspection: 'yes',
    termite_inspection_paid_by: 'seller'
  };
}

// Generate sample data for entity buyers
export function generateEntitySampleData(stateCode: string): SampleData {
  const baseData = generateSampleData(stateCode);
  const entitySuffix = getRandomElement(entityNames);
  
  return {
    ...baseData,
    buyer_1_entity_type: 'entity',
    buyer_1_entity_name: `${getRandomElement(['Alpha', 'Beta', 'Gamma', 'Delta', 'Omega'])} ${entitySuffix}, LLC`,
    buyer_1_name: `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}, Manager`,
    buyer_1_title: 'Managing Member'
  };
}

// Generate sample data for multiple buyers
export function generateMultipleBuyersSampleData(stateCode: string, buyerCount: number): SampleData {
  const baseData = generateSampleData(stateCode);
  const data: SampleData = {
    ...baseData,
    buyer_count: buyerCount.toString()
  };
  
  for (let i = 2; i <= buyerCount; i++) {
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    
    data[`buyer_${i}_name`] = `${firstName} ${lastName}`;
    data[`buyer_${i}_entity_type`] = 'individual';
    data[`buyer_${i}_phone`] = generatePhoneNumber();
    data[`buyer_${i}_email`] = generateEmail(firstName, lastName);
  }
  
  return data;
}

// Generate sample data based on property type
export function generatePropertyTypeSampleData(stateCode: string, propertyType: string): SampleData {
  const baseData = generateSampleData(stateCode);
  
  switch (propertyType) {
    case 'condo':
      return {
        ...baseData,
        property_type: 'condo',
        hoa_dues: '350',
        hoa_dues_frequency: 'monthly',
        hoa_special_assessment: 'no',
        condo_unit_number: Math.floor(Math.random() * 500) + 100,
        building_name: `${getRandomElement(['Sunset', 'Ocean', 'Bay', 'Palm'])} Towers`
      };
      
    case 'townhouse':
      return {
        ...baseData,
        property_type: 'townhouse',
        hoa_dues: '200',
        hoa_dues_frequency: 'monthly'
      };
      
    case 'multi_family':
      return {
        ...baseData,
        property_type: 'multi_family',
        number_of_units: '4',
        monthly_rental_income: '8500',
        current_occupancy: '100%'
      };
      
    case 'land':
      return {
        ...baseData,
        property_type: 'land',
        lot_size_acres: (Math.random() * 5 + 0.5).toFixed(2),
        zoning: 'Residential',
        utilities_available: 'Electric, Water, Sewer'
      };
      
    default:
      return baseData;
  }
}