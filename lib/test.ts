import { RequiredForm, PropertyType } from '@/app/types';

// State abbreviation to name mapping
const STATE_NAMES: Record<string, string> = {
  'AL': 'Alabama',
  'AK': 'Alaska',
  'AZ': 'Arizona',
  'AR': 'Arkansas',
  'CA': 'California',
  'CO': 'Colorado',
  'CT': 'Connecticut',
  'DE': 'Delaware',
  'FL': 'Florida',
  'GA': 'Georgia',
  'HI': 'Hawaii',
  'ID': 'Idaho',
  'IL': 'Illinois',
  'IN': 'Indiana',
  'IA': 'Iowa',
  'KS': 'Kansas',
  'KY': 'Kentucky',
  'LA': 'Louisiana',
  'ME': 'Maine',
  'MD': 'Maryland',
  'MA': 'Massachusetts',
  'MI': 'Michigan',
  'MN': 'Minnesota',
  'MS': 'Mississippi',
  'MO': 'Missouri',
  'MT': 'Montana',
  'NE': 'Nebraska',
  'NV': 'Nevada',
  'NH': 'New Hampshire',
  'NJ': 'New Jersey',
  'NM': 'New Mexico',
  'NY': 'New York',
  'NC': 'North Carolina',
  'ND': 'North Dakota',
  'OH': 'Ohio',
  'OK': 'Oklahoma',
  'OR': 'Oregon',
  'PA': 'Pennsylvania',
  'RI': 'Rhode Island',
  'SC': 'South Carolina',
  'SD': 'South Dakota',
  'TN': 'Tennessee',
  'TX': 'Texas',
  'UT': 'Utah',
  'VT': 'Vermont',
  'VA': 'Virginia',
  'WA': 'Washington',
  'WV': 'West Virginia',
  'WI': 'Wisconsin',
  'WY': 'Wyoming'
};

// Base required forms for all states
const BASE_REQUIRED_FORMS: RequiredForm[] = [
  {
    id: 'purchase_agreement',
    name: 'Purchase and Sale Agreement',
    description: 'The main contract outlining the terms of the property sale',
    required: true
  },
  {
    id: 'property_disclosure',
    name: 'Property Disclosure Statement',
    description: 'Seller disclosure of known property conditions and defects',
    required: true
  },
  {
    id: 'lead_paint_disclosure',
    name: 'Lead-Based Paint Disclosure',
    description: 'Required for homes built before 1978',
    required: false // Will be set to true based on yearBuilt
  }
];

// State-specific additional forms
const STATE_SPECIFIC_FORMS: Record<string, RequiredForm[]> = {
  'FL': [
    {
      id: 'fl_property_condition_disclosure',
      name: 'Florida Property Condition Disclosure',
      description: 'Florida-specific property condition disclosure requirements',
      required: true
    },
    {
      id: 'fl_homeowners_association_disclosure',
      name: 'Homeowners Association Disclosure',
      description: 'Disclosure of HOA fees, rules, and restrictions',
      required: false
    }
  ],
  'CA': [
    {
      id: 'ca_transfer_disclosure_statement',
      name: 'California Transfer Disclosure Statement',
      description: 'California-specific seller disclosure requirements',
      required: true
    },
    {
      id: 'ca_natural_hazard_disclosure',
      name: 'Natural Hazard Disclosure Statement',
      description: 'Disclosure of natural hazard zones (earthquake, flood, fire, etc.)',
      required: true
    }
  ],
  'TX': [
    {
      id: 'tx_seller_disclosure_notice',
      name: 'Texas Seller Disclosure Notice',
      description: 'Texas-specific property condition disclosure',
      required: true
    },
    {
      id: 'tx_addendum_for_property_subject_to_mandatory_membership',
      name: 'Addendum for Property Subject to Mandatory Membership',
      description: 'Required for properties in mandatory HOA communities',
      required: false
    }
  ]
};

// Property type specific forms
const PROPERTY_TYPE_FORMS: Record<PropertyType, RequiredForm[]> = {
  'SINGLE_FAMILY': [],
  'CONDO': [
    {
      id: 'condo_documents',
      name: 'Condominium Documents',
      description: 'HOA bylaws, financial statements, and meeting minutes',
      required: true
    },
    {
      id: 'condo_questionnaire',
      name: 'Condominium Questionnaire',
      description: 'Information about HOA fees, special assessments, and pending litigation',
      required: true
    }
  ],
  'TOWNHOUSE': [
    {
      id: 'hoa_documents',
      name: 'HOA Documents',
      description: 'Homeowners association governing documents and financial information',
      required: false
    }
  ],
  'MULTI_FAMILY': [
    {
      id: 'rental_income_statement',
      name: 'Rental Income Statement',
      description: 'Documentation of current rental income and lease agreements',
      required: false
    }
  ],
  'LAND': [
    {
      id: 'survey',
      name: 'Property Survey',
      description: 'Professional survey showing property boundaries and easements',
      required: true
    },
    {
      id: 'zoning_information',
      name: 'Zoning Information',
      description: 'Current zoning classification and permitted uses',
      required: false
    }
  ],
  'MOBILE_HOME': [
    {
      id: 'mobile_home_title',
      name: 'Mobile Home Title',
      description: 'Title documentation for the mobile home unit',
      required: true
    }
  ]
};

/**
 * Get the full state name from abbreviation
 */
export function getStateName(stateAbbr: string): string {
  const upperAbbr = stateAbbr.toUpperCase();
  return STATE_NAMES[upperAbbr] || stateAbbr;
}

/**
 * Get required forms for a specific state and property type
 */
export function getRequiredForms(
  state: string, 
  propertyType: PropertyType = 'SINGLE_FAMILY', 
  yearBuilt?: number
): RequiredForm[] {
  const upperState = state.toUpperCase();
  
  // Start with base forms
  let forms = [...BASE_REQUIRED_FORMS];
  
  // Update lead paint disclosure requirement based on year built
  if (yearBuilt && yearBuilt < 1978) {
    forms = forms.map(form => 
      form.id === 'lead_paint_disclosure' 
        ? { ...form, required: true }
        : form
    );
  }
  
  // Add state-specific forms
  const stateSpecificForms = STATE_SPECIFIC_FORMS[upperState] || [];
  forms = [...forms, ...stateSpecificForms];
  
  // Add property type specific forms
  const propertyTypeForms = PROPERTY_TYPE_FORMS[propertyType] || [];
  forms = [...forms, ...propertyTypeForms];
  
  return forms;
}

/**
 * Get forms by agreement type (for different subscription levels)
 */
export function getFormsByAgreementType(
  agreementType: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL',
  state: string,
  propertyType: PropertyType = 'SINGLE_FAMILY',
  yearBuilt?: number
): RequiredForm[] {
  const allForms = getRequiredForms(state, propertyType, yearBuilt);
  
  switch (agreementType) {
    case 'PARTIAL_FREE':
      // Free version only includes basic purchase agreement
      return allForms.filter(form => form.id === 'purchase_agreement');
      
    case 'PARTIAL_PAID':
      // Paid version includes required forms only
      return allForms.filter(form => form.required);
      
    case 'OFFICIAL':
      // Official version includes all forms
      return allForms;
      
    default:
      return allForms;
  }
}
