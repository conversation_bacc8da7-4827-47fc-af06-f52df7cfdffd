{"name": "formflorida-landing", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.2.0"}}