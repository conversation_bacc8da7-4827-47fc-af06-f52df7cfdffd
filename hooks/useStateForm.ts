import { useState, useCallback } from 'react';

interface StateFormData {
  formTitle: string;
  formId: string;
  stateCode: string;
  stateName: string;
  sections: any[];
  fieldTypes: any;
  conditionalOperators: any;
  validation: any;
}

export function useStateForm() {
  const [stateFormData, setStateFormData] = useState<StateFormData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadStateForm = useCallback(async (stateCode: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Load the state-specific form JSON
      const response = await fetch(`/api/forms/state/${stateCode}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load form for ${stateCode}`);
      }

      const formData = await response.json();
      setStateFormData(formData);
    } catch (err) {
      console.error('Error loading state form:', err);
      setError(err instanceof Error ? err.message : 'Failed to load form');
      setStateFormData(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    stateFormData,
    loadStateForm,
    isLoading,
    error,
  };
}