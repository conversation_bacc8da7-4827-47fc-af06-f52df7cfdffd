"use client"

import React, { useState, useEffect } from 'react';
import { FileText, CheckCircle, Clock, Zap, ArrowRight, Users, MapPin, DollarSign } from 'lucide-react';

export function SmartFormSection() {
  const [formProgress, setFormProgress] = useState(0);
  const [completedFields, setCompletedFields] = useState<string[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  const formFields = [
    { id: 'property-address', label: 'Property Address', value: '123 Ocean Drive, Miami Beach, FL 33139' },
    { id: 'legal-description', label: 'Legal Description', value: 'Lot 15, Block A, Ocean View Estates' },
    { id: 'tax-id', label: 'Tax ID Number', value: '02-3141-001-0150' },
    { id: 'county', label: 'County Information', value: 'Miami-Dade County, Florida' },
    { id: 'seller-name', label: 'Current Owner/Seller', value: '<PERSON> <PERSON> <PERSON>' },
    { id: 'property-value', label: 'Estimated Market Value', value: '$850,000' },
    { id: 'zoning', label: 'Zoning Classification', value: 'Residential Single Family (RS-1)' },
    { id: 'lot-size', label: 'Lot Size', value: '0.25 acres (10,890 sq ft)' }
  ];

  // Animate form filling
  useEffect(() => {
    const animateForm = () => {
      setIsAnimating(true);
      setFormProgress(0);
      setCompletedFields([]);

      formFields.forEach((field, index) => {
        setTimeout(() => {
          setCompletedFields(prev => [...prev, field.id]);
          setFormProgress(((index + 1) / formFields.length) * 100);
          
          if (index === formFields.length - 1) {
            setTimeout(() => setIsAnimating(false), 500);
          }
        }, (index + 1) * 800);
      });
    };

    // Start animation after component mounts
    const timer = setTimeout(animateForm, 1000);
    return () => clearTimeout(timer);
  }, []);

  const restartAnimation = () => {
    setFormProgress(0);
    setCompletedFields([]);
    setIsAnimating(true);

    formFields.forEach((field, index) => {
      setTimeout(() => {
        setCompletedFields(prev => [...prev, field.id]);
        setFormProgress(((index + 1) / formFields.length) * 100);
        
        if (index === formFields.length - 1) {
          setTimeout(() => setIsAnimating(false), 500);
        }
      }, (index + 1) * 600);
    });
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-full text-[#006AFF] text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>INTELLIGENT FORM AUTOMATION</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
              Smart Form Pre-Population
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Watch how property data automatically fills your Florida purchase agreement forms. No more manual data entry or copy-paste errors.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Left Side - Form Demo */}
            <div className="space-y-6">
              <div className="bg-slate-50 rounded-2xl p-8 border border-slate-200">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-slate-900">Florida Purchase Agreement</h3>
                  <div className="flex items-center gap-2 text-sm text-slate-600">
                    <Clock className="h-4 w-4" />
                    <span>Auto-filling...</span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-700">Form Completion</span>
                    <span className="text-sm font-medium text-[#006AFF]">{Math.round(formProgress)}%</span>
                  </div>
                  <div className="w-full bg-slate-200 rounded-full h-2">
                    <div 
                      className="bg-[#006AFF] h-2 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${formProgress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="space-y-4">
                  {formFields.map((field, index) => (
                    <div 
                      key={field.id}
                      className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                        completedFields.includes(field.id)
                          ? 'border-green-300 bg-green-50'
                          : 'border-slate-200 bg-white'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <label className="text-sm font-medium text-slate-700">{field.label}</label>
                        {completedFields.includes(field.id) && (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-xs font-medium">Auto-filled</span>
                          </div>
                        )}
                      </div>
                      <div className={`text-sm transition-all duration-300 ${
                        completedFields.includes(field.id) 
                          ? 'text-slate-900 font-medium' 
                          : 'text-slate-400'
                      }`}>
                        {completedFields.includes(field.id) ? field.value : 'Waiting for data...'}
                      </div>
                    </div>
                  ))}
                </div>

                <button 
                  onClick={restartAnimation}
                  className="w-full mt-6 bg-[#006AFF] hover:bg-[#0056CC] text-white py-3 px-4 rounded-lg font-semibold transition-all flex items-center justify-center gap-2"
                >
                  <Zap className="h-4 w-4" />
                  Watch Again
                </button>
              </div>
            </div>

            {/* Right Side - Benefits */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Why Realtors Love This Feature</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Clock className="h-6 w-6 text-[#006AFF]" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">Save 45+ Minutes Per Agreement</h4>
                      <p className="text-slate-600">
                        Eliminate manual data entry. What used to take nearly an hour now happens in seconds.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="h-6 w-6 text-[#006AFF]" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">Zero Data Entry Errors</h4>
                      <p className="text-slate-600">
                        No more typos, wrong addresses, or incorrect legal descriptions. Perfect accuracy every time.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Users className="h-6 w-6 text-[#006AFF]" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">Impress Your Clients</h4>
                      <p className="text-slate-600">
                        Professional, accurate documents delivered faster than ever. Your clients will notice the difference.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Zap className="h-6 w-6 text-[#006AFF]" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">Close More Deals</h4>
                      <p className="text-slate-600">
                        Spend less time on paperwork and more time with clients. Faster turnaround means happier customers.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-xl p-6">
                <h4 className="text-lg font-bold text-slate-900 mb-4">Real Results from Florida Realtors</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#006AFF] mb-1">97%</div>
                    <div className="text-sm text-slate-600">Time Saved</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#006AFF] mb-1">100%</div>
                    <div className="text-sm text-slate-600">Accuracy Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#006AFF] mb-1">45min</div>
                    <div className="text-sm text-slate-600">Saved Per Deal</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#006AFF] mb-1">$2,400</div>
                    <div className="text-sm text-slate-600">Monthly Savings</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
