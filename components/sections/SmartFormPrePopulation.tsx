"use client";

import React, { useState, useEffect } from 'react';
import { FileText, CheckCircle, Clock, Zap } from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

export function SmartFormPrePopulation() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 });
  const [formData, setFormData] = useState({
    owner: '',
    address: '',
    county: '',
    legalDescription: ''
  });
  const [progress, setProgress] = useState({
    owner: 0,
    property: 0,
    legal: 0
  });

  useEffect(() => {
    if (isIntersecting) {
      // Simulate form population with animation
      const demoData = {
        owner: '<PERSON> & <PERSON>',
        address: '742 Evergreen Terrace, Orlando, FL 32801',
        county: 'Orange County',
        legalDescription: 'LOT 12, BLOCK 5, EVERGREEN ESTATES'
      };

      // Animate form filling
      setTimeout(() => setFormData(prev => ({ ...prev, owner: demoData.owner })), 500);
      setTimeout(() => setProgress(prev => ({ ...prev, owner: 100 })), 600);
      
      setTimeout(() => setFormData(prev => ({ ...prev, address: demoData.address })), 1500);
      setTimeout(() => setProgress(prev => ({ ...prev, property: 100 })), 1600);
      
      setTimeout(() => setFormData(prev => ({ ...prev, county: demoData.county })), 2500);
      
      setTimeout(() => setFormData(prev => ({ ...prev, legalDescription: demoData.legalDescription })), 3500);
      setTimeout(() => setProgress(prev => ({ ...prev, legal: 100 })), 3600);
    } else {
      // Reset when out of view
      setFormData({
        owner: '',
        address: '',
        county: '',
        legalDescription: ''
      });
      setProgress({
        owner: 0,
        property: 0,
        legal: 0
      });
    }
  }, [isIntersecting]);

  const confidenceScore = Math.round((progress.owner + progress.property + progress.legal) / 3);
  const timeSaved = confidenceScore > 0 ? Math.round(confidenceScore * 0.45) : 0;

  return (
    <section className="py-20 bg-slate-50 border-t border-amber-600/20" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-8">
            <div className="w-2 h-2 bg-amber-600 rounded-full animate-pulse"></div>
            <span>AUTOMATED FORM COMPLETION</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
            Smart Document Pre-Population
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Our intelligent system automatically populates complex legal forms with retrieved property data,
            eliminating manual entry errors and ensuring professional accuracy in every document.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-2xl border-2 border-amber-600/20 p-10 relative">
            {/* Document header styling */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600"></div>
            <div className="grid md:grid-cols-2 gap-10">
              {/* Left Side - Form Fields */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Purchase Agreement Fields
                  </h3>
                </div>

                {/* Seller Name Field */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Seller Name(s)</label>
                  <input
                    type="text"
                    value={formData.owner}
                    className="w-full px-4 py-4 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all bg-white text-slate-800 placeholder-slate-500 font-medium"
                    placeholder="Auto-populating from property data..."
                    readOnly
                  />
                </div>

                {/* Property Address */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Property Address</label>
                  <input
                    type="text"
                    value={formData.address}
                    className="w-full px-4 py-4 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all bg-white text-slate-800 placeholder-slate-500 font-medium"
                    placeholder="Auto-populating from property data..."
                    readOnly
                  />
                </div>

                {/* County */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">County</label>
                  <input
                    type="text"
                    value={formData.county}
                    className="w-full px-4 py-4 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all bg-white text-slate-800 placeholder-slate-500 font-medium"
                    placeholder="Auto-populating from property data..."
                    readOnly
                  />
                </div>

                {/* Legal Description */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Legal Description</label>
                  <textarea
                    value={formData.legalDescription}
                    className="w-full px-4 py-4 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all h-24 resize-none bg-white text-slate-800 placeholder-slate-500 font-medium"
                    placeholder="Auto-populating from property data..."
                    readOnly
                  />
                </div>
              </div>

              {/* Right Side - Progress & Confidence */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Automation Progress
                  </h3>
                </div>

                {/* Progress Bars */}
                <div className="space-y-6">
                  <div className="space-y-3">
                    <div className="flex justify-between text-base">
                      <span className="text-slate-700 font-medium">Owner Information</span>
                      <span className="text-amber-600 font-bold">{progress.owner}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-amber-500 to-yellow-500 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${progress.owner}%` }}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-base">
                      <span className="text-slate-700 font-medium">Property Details</span>
                      <span className="text-amber-600 font-bold">{progress.property}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-amber-500 to-yellow-500 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${progress.property}%` }}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-base">
                      <span className="text-slate-700 font-medium">Legal Information</span>
                      <span className="text-amber-600 font-bold">{progress.legal}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-amber-500 to-yellow-500 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${progress.legal}%` }}
                      />
                    </div>
                  </div>
                </div>

                {/* Confidence Score */}
                <div className="bg-green-50 border-2 border-green-200 rounded-lg p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    <span className="font-semibold text-green-800 text-lg">Data Confidence Score</span>
                  </div>
                  <div className="text-4xl font-bold text-green-700 mb-2">{confidenceScore}%</div>
                  <p className="text-sm text-green-600">
                    {confidenceScore > 90 ? 'Excellent data quality from PropBolt system' : 'Waiting for property data...'}
                  </p>
                </div>

                {/* Time Saved */}
                <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="h-6 w-6 text-blue-600" />
                    <span className="font-semibold text-blue-800 text-lg">Time Saved</span>
                  </div>
                  <div className="text-4xl font-bold text-blue-700 mb-2">{timeSaved} min</div>
                  <p className="text-sm text-blue-600">vs. manual data entry</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}