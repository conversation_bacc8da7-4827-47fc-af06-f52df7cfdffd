"use client"

import { useEffect, useState } from "react"
import { Search, FileText, Download, CheckCircle, ArrowRight } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver"

export function ProcessSteps() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 })
  const [currentStep, setCurrentStep] = useState(0)

  useEffect(() => {
    if (isIntersecting) {
      // Animate steps one by one
      const timeouts = [
        setTimeout(() => setCurrentStep(1), 500),
        setTimeout(() => setCurrentStep(2), 1200),
        setTimeout(() => setCurrentStep(3), 1900),
        setTimeout(() => setCurrentStep(4), 2600),
      ]
      
      return () => {
        timeouts.forEach(timeout => clearTimeout(timeout))
      }
    }
  }, [isIntersecting])

  const steps = [
    {
      icon: Search,
      title: "Enter Property Address",
      description: "Simply type in the property address and our system instantly retrieves comprehensive property data from 150M+ records.",
      color: "blue",
      delay: 0
    },
    {
      icon: FileText,
      title: "Smart Form Generation",
      description: "Our AI automatically populates all required fields with property details, owner information, and legal descriptions.",
      color: "amber",
      delay: 700
    },
    {
      icon: CheckCircle,
      title: "Legal Compliance Check",
      description: "Built-in compliance engine ensures all state-specific requirements and disclosures are included automatically.",
      color: "green",
      delay: 1400
    },
    {
      icon: Download,
      title: "Download Professional PDF",
      description: "Receive a legally compliant, professional purchase agreement ready for signatures in under 90 seconds.",
      color: "purple",
      delay: 2100
    }
  ]

  const getStepClasses = (index: number) => {
    const isActive = currentStep > index

    const iconClasses = [
      isActive ? 'bg-blue-600 text-white shadow-lg scale-110' : 'bg-blue-100 text-blue-600',
      isActive ? 'bg-amber-600 text-white shadow-lg scale-110' : 'bg-amber-100 text-amber-600',
      isActive ? 'bg-green-600 text-white shadow-lg scale-110' : 'bg-green-100 text-green-600',
      isActive ? 'bg-purple-600 text-white shadow-lg scale-110' : 'bg-purple-100 text-purple-600'
    ]

    return {
      container: `transform transition-all duration-700 ${
        isActive ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-8 opacity-30 scale-95'
      }`,
      icon: `w-16 h-16 rounded-xl flex items-center justify-center transition-all duration-500 ${iconClasses[index]}`,
      title: `text-xl font-bold transition-colors duration-500 font-serif ${
        isActive ? 'text-slate-800' : 'text-slate-500'
      }`,
      description: `transition-colors duration-500 ${
        isActive ? 'text-slate-600' : 'text-slate-400'
      }`
    }
  }

  return (
    <section className="py-20 bg-slate-50" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-amber-600 rounded-full animate-pulse"></div>
              <span>HOW IT WORKS</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
              From Address to Agreement in 4 Simple Steps
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Our streamlined process transforms property addresses into professional purchase agreements 
              with intelligent automation and legal compliance.
            </p>
          </div>

          {/* Process Steps */}
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-8 left-8 right-8 h-1 bg-slate-200 hidden lg:block">
              <div 
                className="h-full bg-gradient-to-r from-blue-600 via-amber-600 via-green-600 to-purple-600 transition-all duration-2000 ease-out"
                style={{ width: `${(currentStep / 4) * 100}%` }}
              />
            </div>

            <div className="grid lg:grid-cols-4 gap-8 relative">
              {steps.map((step, index) => {
                const classes = getStepClasses(index)
                const StepIcon = step.icon
                
                return (
                  <div key={index} className={classes.container}>
                    <div className="bg-white rounded-lg p-8 shadow-lg border border-slate-200 h-full relative">
                      {/* Step Number */}
                      <div className="absolute -top-4 -left-4 w-8 h-8 bg-slate-800 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      
                      {/* Icon */}
                      <div className="mb-6">
                        <div className={classes.icon}>
                          <StepIcon className="h-8 w-8" />
                        </div>
                      </div>
                      
                      {/* Content */}
                      <h3 className={`${classes.title} mb-4`}>
                        {step.title}
                      </h3>
                      <p className={`${classes.description} leading-relaxed`}>
                        {step.description}
                      </p>
                      
                      {/* Arrow for desktop */}
                      {index < steps.length - 1 && (
                        <div className="hidden lg:block absolute -right-4 top-1/2 transform -translate-y-1/2">
                          <ArrowRight className={`h-6 w-6 transition-colors duration-500 ${
                            currentStep > index ? 'text-slate-600' : 'text-slate-300'
                          }`} />
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <div className={`transform transition-all duration-1000 ${
              currentStep >= 4 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
            }`}>
              <button className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-bold rounded-lg hover:from-amber-700 hover:to-yellow-700 transition-all shadow-lg hover:shadow-xl transform hover:scale-105">
                <FileText className="h-5 w-5" />
                Start Creating Your Agreement
                <ArrowRight className="h-5 w-5" />
              </button>
              <p className="text-slate-600 mt-4 text-sm">
                Professional documents ready in under 90 seconds
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
