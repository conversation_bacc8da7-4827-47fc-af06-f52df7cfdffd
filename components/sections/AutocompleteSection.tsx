"use client"

import React, { useState, useEffect } from 'react';
import { MapPin, Search, CheckCircle, Users, DollarSign, FileText, Building } from 'lucide-react';

interface AutocompleteSectionProps {
  onAddressInputFocus: () => void;
}

export function AutocompleteSection({ onAddressInputFocus }: AutocompleteSectionProps) {
  const [propertyData, setPropertyData] = useState({
    address: '',
    owner: '',
    value: '',
    legalDescription: '',
    taxId: '',
    county: ''
  });
  const [isTyping, setIsTyping] = useState(false);

  // Typing animation function
  const animateTyping = (field: string, text: string, delay: number) => {
    setTimeout(() => {
      let currentIndex = 0;
      const typingInterval = setInterval(() => {
        if (currentIndex <= text.length) {
          setPropertyData(prev => ({
            ...prev,
            [field]: text.substring(0, currentIndex)
          }));
          currentIndex++;
        } else {
          clearInterval(typingInterval);
        }
      }, 50);
    }, delay);
  };

  // Demo data animation
  useEffect(() => {
    const demoData = {
      address: '123 Ocean Drive, Miami Beach, FL 33139',
      owner: 'Sarah & Michael Rodriguez',
      value: '$850,000',
      legalDescription: 'Lot 15, Block A, Ocean View Estates',
      taxId: '02-3141-001-0150',
      county: 'Miami-Dade'
    };

    // Reset data
    setPropertyData({
      address: '',
      owner: '',
      value: '',
      legalDescription: '',
      taxId: '',
      county: ''
    });

    setIsTyping(true);
    animateTyping('address', demoData.address, 500);
    animateTyping('owner', demoData.owner, 1500);
    animateTyping('value', demoData.value, 2500);
    animateTyping('legalDescription', demoData.legalDescription, 3500);
    animateTyping('taxId', demoData.taxId, 4500);
    animateTyping('county', demoData.county, 5500);

    setTimeout(() => setIsTyping(false), 6000);
  }, []);

  return (
    <section className="py-20 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-full text-[#006AFF] text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>SMART ADDRESS LOOKUP</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
              Instant Property Intelligence
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Simply type an address and watch as we automatically retrieve all property details, owner information, and legal descriptions in real-time.
            </p>
          </div>

          {/* Demo Content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-slate-200">
            <div className="grid md:grid-cols-2 gap-12">
              {/* Left Side - Address Input Demo */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-slate-900 mb-6 flex items-center gap-3">
                  <Search className="h-6 w-6 text-[#006AFF]" />
                  Smart Address Search
                </h3>
                
                <div className="relative">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <Search className="h-5 w-5 text-slate-400" />
                  </div>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all text-lg"
                    placeholder="Start typing address..."
                    readOnly
                  />
                  {isTyping && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#006AFF]"></div>
                    </div>
                  )}
                </div>
                
                {propertyData.address && (
                  <div className="bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-xl p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-2 text-[#006AFF] text-sm font-medium mb-2">
                      <MapPin className="h-4 w-4" />
                      <span>Property Found!</span>
                    </div>
                    <p className="text-sm text-slate-600">FormFlorida API retrieving comprehensive property details...</p>
                  </div>
                )}

                <div className="text-center pt-4">
                  <button 
                    onClick={onAddressInputFocus}
                    className="bg-[#006AFF] hover:bg-[#0056CC] text-white px-6 py-3 rounded-lg font-semibold transition-all shadow-lg hover:shadow-xl"
                  >
                    Try It Yourself
                  </button>
                </div>
              </div>

              {/* Right Side - Property Data Display */}
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-slate-900 mb-6 flex items-center gap-3">
                  <FileText className="h-6 w-6 text-[#006AFF]" />
                  Retrieved Property Data
                </h3>
                
                <div className="space-y-3">
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.owner ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Current Owner</span>
                      {propertyData.owner && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-slate-900 font-semibold text-lg">
                      {propertyData.owner || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.value ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Estimated Value</span>
                      {propertyData.value && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-green-600 font-bold text-2xl">
                      {propertyData.value || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.legalDescription ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <Building className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Legal Description</span>
                      {propertyData.legalDescription && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-slate-900 font-semibold">
                      {propertyData.legalDescription || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                      propertyData.taxId ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-slate-500" />
                        <span className="text-xs font-medium text-slate-600">Tax ID</span>
                        {propertyData.taxId && <CheckCircle className="h-3 w-3 text-green-500" />}
                      </div>
                      <div className="text-slate-900 font-semibold text-sm">
                        {propertyData.taxId || 'Loading...'}
                      </div>
                    </div>
                    <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                      propertyData.county ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-slate-500" />
                        <span className="text-xs font-medium text-slate-600">County</span>
                        {propertyData.county && <CheckCircle className="h-3 w-3 text-green-500" />}
                      </div>
                      <div className="text-slate-900 font-semibold">
                        {propertyData.county || 'Loading...'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="text-center p-6 bg-white rounded-lg shadow-lg border border-slate-200">
              <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Search className="h-6 w-6 text-[#006AFF]" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-2">Instant Lookup</h3>
              <p className="text-slate-600 text-sm">
                No more manual research. Get property details in seconds, not hours.
              </p>
            </div>
            <div className="text-center p-6 bg-white rounded-lg shadow-lg border border-slate-200">
              <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-6 w-6 text-[#006AFF]" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-2">100% Accurate</h3>
              <p className="text-slate-600 text-sm">
                Data sourced directly from official county records and MLS systems.
              </p>
            </div>
            <div className="text-center p-6 bg-white rounded-lg shadow-lg border border-slate-200">
              <div className="w-12 h-12 bg-[#006AFF]/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="h-6 w-6 text-[#006AFF]" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-2">Auto-Fill Ready</h3>
              <p className="text-slate-600 text-sm">
                All data automatically populates your purchase agreement forms.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
