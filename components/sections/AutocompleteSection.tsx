"use client"

import React, { useState, useEffect } from 'react';
import { MapPin, Search, CheckCircle, Users, DollarSign, FileText, Building, Timer } from 'lucide-react';

interface AutocompleteSectionProps {
  onAddressInputFocus: () => void;
}

export function AutocompleteSection({ onAddressInputFocus }: AutocompleteSectionProps) {
  const [propertyData, setPropertyData] = useState({
    address: '',
    owner: '',
    value: '',
    legalDescription: '',
    taxId: '',
    county: ''
  });
  const [isTyping, setIsTyping] = useState(false);

  // Typing animation function
  const animateTyping = (field: string, text: string, delay: number) => {
    setTimeout(() => {
      let currentIndex = 0;
      const typingInterval = setInterval(() => {
        if (currentIndex <= text.length) {
          setPropertyData(prev => ({
            ...prev,
            [field]: text.substring(0, currentIndex)
          }));
          currentIndex++;
        } else {
          clearInterval(typingInterval);
        }
      }, 50);
    }, delay);
  };

  // Continuous demo data animation
  useEffect(() => {
    const demoData = {
      address: '123 Ocean Drive, Miami Beach, FL 33139',
      owner: 'Sarah & Michael Rodriguez',
      value: '$850,000',
      legalDescription: 'Lot 15, Block A, Ocean View Estates',
      taxId: '02-3141-001-0150',
      county: 'Miami-Dade'
    };

    const runAnimation = () => {
      // Reset data
      setPropertyData({
        address: '',
        owner: '',
        value: '',
        legalDescription: '',
        taxId: '',
        county: ''
      });

      setIsTyping(true);
      animateTyping('address', demoData.address, 500);
      animateTyping('owner', demoData.owner, 1500);
      animateTyping('value', demoData.value, 2500);
      animateTyping('legalDescription', demoData.legalDescription, 3500);
      animateTyping('taxId', demoData.taxId, 4500);
      animateTyping('county', demoData.county, 5500);

      setTimeout(() => setIsTyping(false), 6000);
    };

    // Run initial animation
    runAnimation();

    // Set up continuous loop - restart every 8 seconds
    const interval = setInterval(runAnimation, 8000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-slate-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23006AFF' fill-opacity='0.1'%3E%3Cpath d='M20 20.5V18H18v2.5h-2.5V22H18v2.5h2V22h2.5v-1.5H20zM0 38.59l2.59-2.59 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.59 2.59L1.41 5.59 0 4.18V1.41z'/%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-full text-[#006AFF] text-sm font-semibold mb-8 backdrop-blur-sm">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>⚡ INSTANT PROPERTY LOOKUP</span>
              <div className="w-1 h-4 bg-[#006AFF]/50"></div>
              <span>SAVES 30+ MINUTES</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6 leading-tight">
              Stop Googling Property Details
            </h2>
            <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto mb-8 leading-relaxed">
              Type any Florida address and watch our AI instantly pull owner info, legal descriptions, tax records, and market data.
              <span className="block mt-2 text-[#006AFF] font-semibold">No more county websites. No more manual research.</span>
            </p>

            {/* Social Proof */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 border border-green-200 rounded-lg text-green-700 text-sm font-medium">
              <CheckCircle className="h-4 w-4" />
              <span>Used by 2,500+ Florida realtors this month</span>
            </div>
          </div>

          {/* Demo Content */}
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-slate-200">
            <div className="grid md:grid-cols-2 gap-12">
              {/* Left Side - Address Input Demo */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-slate-900 mb-6 flex items-center gap-3">
                  <Search className="h-6 w-6 text-[#006AFF]" />
                  Smart Address Search
                </h3>
                
                <div className="relative">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <Search className="h-5 w-5 text-slate-400" />
                  </div>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all text-lg"
                    placeholder="Start typing address..."
                    readOnly
                  />
                  {isTyping && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#006AFF]"></div>
                    </div>
                  )}
                </div>
                
                {propertyData.address && (
                  <div className="bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-xl p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-2 text-[#006AFF] text-sm font-medium mb-2">
                      <MapPin className="h-4 w-4" />
                      <span>Property Found!</span>
                    </div>
                    <p className="text-sm text-slate-600">FormFlorida API retrieving comprehensive property details...</p>
                  </div>
                )}

                <div className="text-center pt-4">
                  <button 
                    onClick={onAddressInputFocus}
                    className="bg-[#006AFF] hover:bg-[#0056CC] text-white px-6 py-3 rounded-lg font-semibold transition-all shadow-lg hover:shadow-xl"
                  >
                    Try It Yourself
                  </button>
                </div>
              </div>

              {/* Right Side - Property Data Display */}
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-slate-900 mb-6 flex items-center gap-3">
                  <FileText className="h-6 w-6 text-[#006AFF]" />
                  Retrieved Property Data
                </h3>
                
                <div className="space-y-3">
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.owner ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Current Owner</span>
                      {propertyData.owner && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-slate-900 font-semibold text-lg">
                      {propertyData.owner || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.value ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Estimated Value</span>
                      {propertyData.value && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-green-600 font-bold text-2xl">
                      {propertyData.value || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                    propertyData.legalDescription ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <Building className="h-4 w-4 text-slate-500" />
                      <span className="text-sm font-medium text-slate-600">Legal Description</span>
                      {propertyData.legalDescription && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="text-slate-900 font-semibold">
                      {propertyData.legalDescription || 'Loading...'}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                      propertyData.taxId ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-slate-500" />
                        <span className="text-xs font-medium text-slate-600">Tax ID</span>
                        {propertyData.taxId && <CheckCircle className="h-3 w-3 text-green-500" />}
                      </div>
                      <div className="text-slate-900 font-semibold text-sm">
                        {propertyData.taxId || 'Loading...'}
                      </div>
                    </div>
                    <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                      propertyData.county ? 'border-green-300 bg-green-50' : 'border-slate-200 bg-slate-50'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-slate-500" />
                        <span className="text-xs font-medium text-slate-600">County</span>
                        {propertyData.county && <CheckCircle className="h-3 w-3 text-green-500" />}
                      </div>
                      <div className="text-slate-900 font-semibold">
                        {propertyData.county || 'Loading...'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Benefits with ROI */}
          <div className="mt-20">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-slate-900 mb-4">Why Realtors Choose Our Property Lookup</h3>
              <p className="text-lg text-slate-600">Real results from real Florida real estate professionals</p>
            </div>

            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center p-8 bg-white rounded-xl shadow-xl border border-slate-200 hover:shadow-2xl transition-all">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-8 w-8 text-green-600" />
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">30 sec</div>
                <h4 className="text-lg font-bold text-slate-900 mb-2">Instant Lookup</h4>
                <p className="text-slate-600 text-sm">
                  Get complete property details faster than opening a browser tab
                </p>
              </div>

              <div className="text-center p-8 bg-white rounded-xl shadow-xl border border-slate-200 hover:shadow-2xl transition-all">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="h-8 w-8 text-[#006AFF]" />
                </div>
                <div className="text-3xl font-bold text-[#006AFF] mb-2">99.9%</div>
                <h4 className="text-lg font-bold text-slate-900 mb-2">Accuracy Rate</h4>
                <p className="text-slate-600 text-sm">
                  Direct from county records and official MLS databases
                </p>
              </div>

              <div className="text-center p-8 bg-white rounded-xl shadow-xl border border-slate-200 hover:shadow-2xl transition-all">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FileText className="h-8 w-8 text-purple-600" />
                </div>
                <div className="text-3xl font-bold text-purple-600 mb-2">Auto</div>
                <h4 className="text-lg font-bold text-slate-900 mb-2">Form Population</h4>
                <p className="text-slate-600 text-sm">
                  All data flows directly into your purchase agreements
                </p>
              </div>

              <div className="text-center p-8 bg-white rounded-xl shadow-xl border border-slate-200 hover:shadow-2xl transition-all">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Timer className="h-8 w-8 text-orange-600" />
                </div>
                <div className="text-3xl font-bold text-orange-600 mb-2">$1,500</div>
                <h4 className="text-lg font-bold text-slate-900 mb-2">Monthly Savings</h4>
                <p className="text-slate-600 text-sm">
                  Average value of time saved per realtor per month
                </p>
              </div>
            </div>

            {/* Testimonial */}
            <div className="mt-16 bg-gradient-to-r from-[#006AFF]/5 to-blue-50 rounded-2xl p-8 border border-[#006AFF]/20">
              <div className="max-w-4xl mx-auto text-center">
                <div className="text-2xl text-[#006AFF] mb-4">⭐⭐⭐⭐⭐</div>
                <blockquote className="text-xl md:text-2xl text-slate-700 font-medium mb-6 italic">
                  "I used to spend 30-45 minutes researching each property. Now it takes 30 seconds. This tool has literally given me my life back."
                </blockquote>
                <div className="flex items-center justify-center gap-4">
                  <div className="w-12 h-12 bg-[#006AFF] rounded-full flex items-center justify-center text-white font-bold">
                    SM
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-slate-900">Sarah Martinez</div>
                    <div className="text-slate-600">Top Producer, Keller Williams Miami</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
