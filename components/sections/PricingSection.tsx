"use client"

import { useEffect, useState } from "react"
import { Check, Star, Crown, Zap } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver"

export function PricingSection() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 })
  const [animationPhase, setAnimationPhase] = useState(0)

  useEffect(() => {
    if (isIntersecting && animationPhase === 0) {
      // Only animate once when first entering view
      const timeouts = [
        setTimeout(() => setAnimationPhase(1), 200),
        setTimeout(() => setAnimationPhase(2), 400),
        setTimeout(() => setAnimationPhase(3), 600),
      ]

      return () => {
        timeouts.forEach(timeout => clearTimeout(timeout))
      }
    }
  }, [isIntersecting, animationPhase])

  return (
    <section className="py-20 bg-slate-50 border-t border-amber-600/20" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Pricing Header */}
          <div className={`text-center mb-16 transform transition-all duration-1000 ${
            animationPhase >= 1 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-8">
              <Crown className="h-4 w-4" />
              <span>PAY-PER-DOCUMENT PRICING</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto">
              Pay only for what you use. Professional-grade purchase agreements at $25 per document,
              with volume discounts for high-volume wholesalers and brokerages.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className={`grid md:grid-cols-3 gap-8 transform transition-all duration-1000 delay-300 ${
            animationPhase >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            {/* Standard Pricing */}
            <div className="bg-white rounded-lg border-2 border-slate-200 p-8 relative hover:shadow-xl transition-all hover:border-slate-300">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Standard Rate</h3>
                <div className="text-4xl font-bold text-slate-800 mb-2">$25<span className="text-lg text-slate-600">/PDF</span></div>
                <p className="text-slate-600">Perfect for individual agents</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Pay per document generated</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">All 50 states supported</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Property data integration</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Email support</span>
                </li>
              </ul>

              <button className="w-full py-3 px-6 bg-slate-800 text-white font-semibold rounded-lg hover:bg-slate-900 transition-colors shadow-md">
                Generate First PDF
              </button>
            </div>

            {/* Volume Discount - Featured */}
            <div className="bg-white rounded-lg border-2 border-amber-500 p-8 relative hover:shadow-xl transition-all transform scale-105 shadow-lg">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-amber-600 text-white px-4 py-1 rounded-full text-sm font-bold shadow-md">BEST VALUE</span>
              </div>

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Volume Discount</h3>
                <div className="text-4xl font-bold text-amber-600 mb-2">$18<span className="text-lg text-slate-600">/PDF</span></div>
                <p className="text-slate-600">For active wholesalers & teams</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">25+ PDFs per month</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">28% savings per document</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Priority processing</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Phone support</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Bulk document tools</span>
                </li>
              </ul>

              <button className="w-full py-3 px-6 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-bold rounded-lg hover:from-amber-700 hover:to-yellow-700 transition-all shadow-md">
                Start Volume Plan
              </button>
            </div>

            {/* Wholesale Plan */}
            <div className="bg-white rounded-lg border-2 border-slate-200 p-8 relative hover:shadow-xl transition-all hover:border-slate-300">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Wholesale Rate</h3>
                <div className="text-4xl font-bold text-slate-800 mb-2">$12<span className="text-lg text-slate-600">/PDF</span></div>
                <p className="text-slate-600">For high-volume wholesalers</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">100+ PDFs per month</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">52% savings per document</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">Dedicated account manager</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">API access & integrations</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-green-600" />
                  </div>
                  <span className="text-slate-700 font-medium">White-label options</span>
                </li>
              </ul>

              <button className="w-full py-3 px-6 bg-slate-800 text-white font-semibold rounded-lg hover:bg-slate-900 transition-colors shadow-md">
                Contact Sales
              </button>
            </div>
          </div>

          {/* Volume Pricing Table */}
          <div className={`mt-16 transform transition-all duration-1000 delay-600 ${
            animationPhase >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            <div className="bg-slate-50 rounded-lg p-8 border border-slate-200">
              <h3 className="text-2xl font-bold text-slate-800 text-center mb-8 font-serif">Volume Pricing Breakdown</h3>
              <div className="grid md:grid-cols-3 gap-6 text-center">
                <div className="bg-white rounded-lg p-6 border border-slate-200">
                  <div className="text-3xl font-bold text-slate-800 mb-2">$25</div>
                  <div className="text-slate-600 font-medium mb-2">Standard Rate</div>
                  <div className="text-sm text-slate-500">1-24 PDFs per month</div>
                </div>
                <div className="bg-amber-50 rounded-lg p-6 border-2 border-amber-200">
                  <div className="text-3xl font-bold text-amber-600 mb-2">$18</div>
                  <div className="text-slate-800 font-medium mb-2">Volume Discount</div>
                  <div className="text-sm text-slate-600">25-99 PDFs per month</div>
                  <div className="text-xs text-amber-600 font-semibold mt-1">Save 28%</div>
                </div>
                <div className="bg-green-50 rounded-lg p-6 border-2 border-green-200">
                  <div className="text-3xl font-bold text-green-600 mb-2">$12</div>
                  <div className="text-slate-800 font-medium mb-2">Wholesale Rate</div>
                  <div className="text-sm text-slate-600">100+ PDFs per month</div>
                  <div className="text-xs text-green-600 font-semibold mt-1">Save 52%</div>
                </div>
              </div>
            </div>
          </div>

          {/* Guarantee */}
          <div className={`text-center mt-12 transform transition-all duration-1000 delay-800 ${
            animationPhase >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
              <Star className="h-4 w-4" />
              <span className="font-semibold">No setup fees • Pay as you go • Professional support included</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
