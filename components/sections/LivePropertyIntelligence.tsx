"use client";

import React, { useState, useEffect } from 'react';
import { MapPin, Users, DollarSign, FileText, Building, CheckCircle, Search, Sparkles } from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

export function LivePropertyIntelligence() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 });
  const [propertyData, setPropertyData] = useState({
    address: '',
    owner: '',
    value: '',
    legalDescription: '',
    taxId: '',
    county: ''
  });

  // Animated typing effect for property data
  useEffect(() => {
    if (isIntersecting) {
      const demoData = {
        address: '742 Evergreen Terrace, Orlando, FL 32801',
        owner: '<PERSON> <PERSON>',
        value: '$289,000',
        legalDescription: 'LOT 12, BLOCK 5, EVERGREEN ESTATES',
        taxId: '14-28-126-005',
        county: 'Orange County'
      };

      const animateTyping = (key: keyof typeof demoData, text: string, delay: number) => {
        setTimeout(() => {
          setPropertyData(prev => ({ ...prev, [key]: '' }));
          let i = 0;
          const typeInterval = setInterval(() => {
            if (i < text.length) {
              setPropertyData(prev => ({ ...prev, [key]: text.slice(0, i + 1) }));
              i++;
            } else {
              clearInterval(typeInterval);
            }
          }, 50);
        }, delay);
      };

      // Reset and animate
      setPropertyData({
        address: '',
        owner: '',
        value: '',
        legalDescription: '',
        taxId: '',
        county: ''
      });

      animateTyping('address', demoData.address, 500);
      animateTyping('owner', demoData.owner, 1500);
      animateTyping('value', demoData.value, 2500);
      animateTyping('legalDescription', demoData.legalDescription, 3500);
      animateTyping('taxId', demoData.taxId, 4500);
      animateTyping('county', demoData.county, 5500);
    } else {
      // Reset when out of view
      setPropertyData({
        address: '',
        owner: '',
        value: '',
        legalDescription: '',
        taxId: '',
        county: ''
      });
    }
  }, [isIntersecting]);

  return (
    <section className="py-20 bg-slate-50 border-t border-amber-600/20" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-8">
            <div className="w-2 h-2 bg-amber-600 rounded-full animate-pulse"></div>
            <span>INTELLIGENT PROPERTY DATA SYSTEM</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
            Automated Property Intelligence
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Our professional-grade system instantly retrieves comprehensive property data from 150M+ records,
            eliminating manual research and ensuring accuracy in your purchase agreements.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-2xl border-2 border-amber-600/20 p-10 relative">
            {/* Document header styling */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600"></div>
            <div className="grid md:grid-cols-2 gap-10">
              {/* Left Side - Address Input Demo */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <Search className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Property Address Input
                  </h3>
                </div>
                <div className="relative">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <Search className="h-5 w-5 text-slate-400" />
                  </div>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all bg-white text-slate-800 placeholder-slate-500 font-medium"
                    placeholder="Enter property address..."
                    readOnly
                  />
                </div>

                {propertyData.address && (
                  <div className="bg-green-50 border-2 border-green-200 rounded-lg p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-2 text-green-700 text-sm font-semibold mb-2">
                      <MapPin className="h-4 w-4" />
                      <span>Property Located</span>
                    </div>
                    <p className="text-sm text-green-600">Retrieving comprehensive property data...</p>
                  </div>
                )}
              </div>

              {/* Right Side - Property Data Cards */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <DollarSign className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Retrieved Property Data
                  </h3>
                </div>

                {/* Owner Information */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.owner
                    ? 'border-green-300 bg-green-50'
                    : 'border-slate-300 bg-slate-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="h-5 w-5 text-slate-500" />
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Current Owner</span>
                    {propertyData.owner && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                  <p className="text-base text-slate-800 font-semibold">
                    {propertyData.owner || 'Retrieving...'}
                  </p>
                </div>

                {/* Property Value */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.value
                    ? 'border-green-300 bg-green-50'
                    : 'border-slate-300 bg-slate-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <DollarSign className="h-5 w-5 text-slate-500" />
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Estimated Value</span>
                    {propertyData.value && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                  <p className="text-base text-slate-800 font-semibold">
                    {propertyData.value || 'Retrieving...'}
                  </p>
                </div>

                {/* Legal Description */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.legalDescription
                    ? 'border-green-300 bg-green-50'
                    : 'border-slate-300 bg-slate-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="h-5 w-5 text-slate-500" />
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Legal Description</span>
                    {propertyData.legalDescription && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                  <p className="text-base text-slate-800 font-semibold">
                    {propertyData.legalDescription || 'Retrieving...'}
                  </p>
                </div>

                {/* Tax ID */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.taxId
                    ? 'border-green-300 bg-green-50'
                    : 'border-slate-300 bg-slate-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-5 w-5 text-slate-500" />
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Property Tax ID</span>
                    {propertyData.taxId && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                  <p className="text-base text-slate-800 font-semibold">
                    {propertyData.taxId || 'Retrieving...'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}