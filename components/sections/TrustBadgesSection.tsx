"use client"

import { useEffect, useState } from "react"
import { Shield, Award, Users, CheckCircle, Star, Building, Scale, FileText } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver"

export function TrustBadgesSection() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 })
  const [animationPhase, setAnimationPhase] = useState(0)

  useEffect(() => {
    if (isIntersecting) {
      const timeouts = [
        setTimeout(() => setAnimationPhase(1), 200),
        setTimeout(() => setAnimationPhase(2), 600),
        setTimeout(() => setAnimationPhase(3), 1000),
      ]
      
      return () => {
        timeouts.forEach(timeout => clearTimeout(timeout))
      }
    } else {
      setAnimationPhase(0)
    }
  }, [isIntersecting])

  return (
    <section className="py-16 bg-white border-b border-amber-600/10" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Trust Header */}
          <div className={`text-center mb-12 transform transition-all duration-1000 ${
            animationPhase >= 1 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-6">
              <Shield className="h-4 w-4" />
              <span>LICENSED REAL ESTATE PROFESSIONAL</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4 font-serif">
              Built by Licensed Brokers, Trusted by Professionals
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Our platform is developed and maintained by active real estate brokers with decades of experience in purchase agreements and legal compliance.
            </p>
          </div>

          {/* Trust Badges Grid */}
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 transform transition-all duration-1000 delay-300 ${
            animationPhase >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            {/* Licensed Broker */}
            <div className="text-center p-6 bg-slate-50 rounded-lg border border-slate-200 hover:shadow-lg transition-all">
              <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Licensed Broker</h3>
              <p className="text-sm text-slate-600">Active real estate license</p>
            </div>

            {/* Legal Compliance */}
            <div className="text-center p-6 bg-slate-50 rounded-lg border border-slate-200 hover:shadow-lg transition-all">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Scale className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Legal Compliance</h3>
              <p className="text-sm text-slate-600">50-state requirements</p>
            </div>

            {/* Professional Grade */}
            <div className="text-center p-6 bg-slate-50 rounded-lg border border-slate-200 hover:shadow-lg transition-all">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Professional Grade</h3>
              <p className="text-sm text-slate-600">Industry standard forms</p>
            </div>

            {/* Secure Platform */}
            <div className="text-center p-6 bg-slate-50 rounded-lg border border-slate-200 hover:shadow-lg transition-all">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Secure Platform</h3>
              <p className="text-sm text-slate-600">Enterprise security</p>
            </div>
          </div>

          {/* Broker Credentials */}
          <div className={`bg-gradient-to-r from-amber-50 to-yellow-50 border-2 border-amber-200 rounded-lg p-8 transform transition-all duration-1000 delay-600 ${
            animationPhase >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}>
            <div className="grid md:grid-cols-3 gap-8 items-center">
              <div className="text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start gap-2 mb-4">
                  <div className="w-12 h-12 bg-amber-600 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-slate-800 text-lg">Licensed Broker Team</h3>
                    <p className="text-sm text-amber-700">Active Florida Real Estate License</p>
                  </div>
                </div>
                <p className="text-slate-700">
                  Our development team includes active real estate brokers who understand the complexities of purchase agreements.
                </p>
              </div>

              <div className="text-center">
                <div className="text-4xl font-bold text-amber-600 mb-2">15+</div>
                <p className="text-slate-700 font-semibold">Years Experience</p>
                <p className="text-sm text-slate-600">In real estate transactions</p>
              </div>

              <div className="text-center">
                <div className="text-4xl font-bold text-amber-600 mb-2">50</div>
                <p className="text-slate-700 font-semibold">States Supported</p>
                <p className="text-sm text-slate-600">Compliant legal forms</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
