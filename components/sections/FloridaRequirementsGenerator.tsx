"use client";

import React, { useState, useEffect } from 'react';
import { Shield, CheckCircle, Home } from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

export function FloridaRequirementsGenerator() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.3 });
  const [showForms, setShowForms] = useState({
    purchase: false,
    disclosure: false,
    leadPaint: false,
    hoa: false
  });

  useEffect(() => {
    if (isIntersecting && !showForms.purchase) {
      // Only animate once when first entering view
      setTimeout(() => setShowForms(prev => ({ ...prev, purchase: true })), 200);
      setTimeout(() => setShowForms(prev => ({ ...prev, disclosure: true })), 400);
      setTimeout(() => setShowForms(prev => ({ ...prev, leadPaint: true })), 600);
      setTimeout(() => setShowForms(prev => ({ ...prev, hoa: true })), 800);
    }
  }, [isIntersecting, showForms.purchase]);

  const totalForms = Object.values(showForms).filter(Boolean).length;

  return (
    <section className="py-20 bg-white border-t border-amber-600/20" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-700 text-sm font-semibold mb-8">
            <div className="w-2 h-2 bg-amber-600 rounded-full animate-pulse"></div>
            <span>LEGAL COMPLIANCE ENGINE</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
            50-State Legal Compliance
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Our licensed broker-developed system automatically generates state-specific legal requirements,
            ensuring every document meets local regulations and professional standards.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-2xl border-2 border-amber-600/20 p-10 relative">
            {/* Document header styling */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-600 via-yellow-500 to-amber-600"></div>
            <div className="grid md:grid-cols-2 gap-10">
              {/* Left Side - Property Analysis */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <Home className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Legal Requirements Analysis
                  </h3>
                </div>

                {/* Property Characteristics */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Jurisdiction</span>
                    <span className="text-base text-amber-600 font-bold">Florida</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Property Type</span>
                    <span className="text-base text-slate-800 font-semibold">Single Family Residence</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Year Built</span>
                    <span className="text-base text-slate-800 font-semibold">1965</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <span className="text-sm font-semibold text-slate-600 uppercase tracking-wide">HOA Status</span>
                    <span className="text-base text-orange-600 font-semibold">Community Association</span>
                  </div>
                </div>

                {/* Legal Compliance Visual */}
                <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border-2 border-amber-200 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-3">⚖️</div>
                  <h4 className="font-semibold text-amber-700 text-lg mb-2">Licensed Broker Compliance</h4>
                  <p className="text-sm text-amber-600">Analyzing state-specific legal requirements...</p>
                </div>
              </div>

              {/* Right Side - Required Forms */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 font-serif">
                    Required Legal Documents
                  </h3>
                </div>

                {/* Required Forms List */}
                <div className="space-y-4">
                  {/* Purchase Agreement */}
                  <div className={`flex items-center gap-4 p-5 bg-green-50 border-2 border-green-200 rounded-lg transition-all duration-500 ${
                    showForms.purchase ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                  }`}>
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-800 text-lg">Florida Purchase Agreement</h4>
                      <p className="text-sm text-green-600">Required for all Florida transactions</p>
                    </div>
                    <span className="text-xs bg-green-600 text-white px-3 py-1 rounded-full font-semibold">REQUIRED</span>
                  </div>

                  {/* Property Disclosure */}
                  <div className={`flex items-center gap-4 p-5 bg-green-50 border-2 border-green-200 rounded-lg transition-all duration-500 ${
                    showForms.disclosure ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                  }`} style={{ transitionDelay: '0.3s' }}>
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-800 text-lg">Florida Property Disclosure</h4>
                      <p className="text-sm text-green-600">State-mandated seller disclosure</p>
                    </div>
                    <span className="text-xs bg-green-600 text-white px-3 py-1 rounded-full font-semibold">REQUIRED</span>
                  </div>

                  {/* Lead Paint (conditional) */}
                  <div className={`flex items-center gap-4 p-5 bg-red-50 border-2 border-red-200 rounded-lg transition-all duration-500 ${
                    showForms.leadPaint ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                  }`} style={{ transitionDelay: '0.6s' }}>
                    <Shield className="h-6 w-6 text-red-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-red-800 text-lg">Lead Paint Disclosure</h4>
                      <p className="text-sm text-red-600">Required for homes built before 1978</p>
                    </div>
                    <span className="text-xs bg-red-600 text-white px-3 py-1 rounded-full font-semibold">REQUIRED</span>
                  </div>

                  {/* HOA Disclosure (optional) */}
                  <div className={`flex items-center gap-4 p-5 bg-yellow-50 border-2 border-yellow-200 rounded-lg transition-all duration-500 ${
                    showForms.hoa ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                  }`} style={{ transitionDelay: '0.9s' }}>
                    <Home className="h-6 w-6 text-yellow-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-yellow-800 text-lg">HOA Disclosure</h4>
                      <p className="text-sm text-yellow-600">HOA detected - disclosure recommended</p>
                    </div>
                    <span className="text-xs bg-yellow-600 text-white px-3 py-1 rounded-full font-semibold">OPTIONAL</span>
                  </div>
                </div>

                {/* Summary */}
                <div className="bg-amber-50 border-2 border-amber-200 rounded-lg p-6 mt-8">
                  <h4 className="font-semibold text-amber-800 text-lg mb-4">Document Package Summary</h4>
                  <div className="space-y-3 text-base">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-700 font-medium">Required Forms:</span>
                      <span className="font-bold text-slate-800 text-lg">3</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-700 font-medium">Optional Forms:</span>
                      <span className="font-bold text-slate-800 text-lg">1</span>
                    </div>
                    <div className="flex justify-between items-center pt-2 border-t border-amber-200">
                      <span className="text-amber-700 font-semibold">Total Package:</span>
                      <span className="font-bold text-amber-700 text-xl">{totalForms} documents</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}