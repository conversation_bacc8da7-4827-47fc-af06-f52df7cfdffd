"use client";

import React from 'react';

export function SectionConnector() {
  return (
    <div className="relative py-8 bg-gradient-to-b from-transparent via-slate-800/50 to-transparent">
      <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gradient-to-b from-blue-500 via-purple-400 to-blue-500 opacity-40"></div>
      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
        <div className="absolute inset-0 w-4 h-4 bg-blue-400 rounded-full animate-ping opacity-75"></div>
      </div>
    </div>
  );
}