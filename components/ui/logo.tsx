import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
}

export function Logo({ className }: LogoProps) {
  return (
    <div className={cn("flex items-center font-bold text-2xl", className)}>
      <div className="w-3 h-3 bg-[#006AFF] rounded-full mr-2"></div>
      <span className="text-[#006AFF]">Prop</span>
      <span className="text-slate-800">Bolt</span>
    </div>
  );
}
