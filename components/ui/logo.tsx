import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
}

export function Logo({ className }: LogoProps) {
  return (
    <div className={cn("flex items-center font-bold text-3xl", className)}>
      <span className="text-gray-800 text-4xl font-light mr-0.5">{`{`}</span>
      <span className="text-[#006AFF]">Prop</span>
      <span className="text-gray-800">Bolt</span>
      <span className="text-gray-800 text-4xl font-light ml-0.5">{`}`}</span>
    </div>
  );
}
