'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, Info, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FormField } from './fields/FormField';
import { evaluateCondition } from '@/lib/form-utils';

interface DynamicFormEngineProps {
  formData: any;
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  isLoading?: boolean;
  className?: string;
}

export function DynamicFormEngine({
  formData,
  values,
  onChange,
  isLoading = false,
  className,
}: DynamicFormEngineProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Initialize expanded sections (expand first section by default)
  useEffect(() => {
    if (formData?.sections?.length > 0) {
      setExpandedSections(new Set([formData.sections[0].id]));
    }
  }, [formData]);

  // Handle field change
  const handleFieldChange = (fieldId: string, value: any) => {
    onChange({ ...values, [fieldId]: value });
    
    // Mark field as touched
    setTouchedFields((prev) => new Set(prev).add(fieldId));
    
    // Clear error for this field
    if (errors[fieldId]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Check if field should be visible based on conditions
  const isFieldVisible = (field: any): boolean => {
    if (!field.conditional) return true;
    return evaluateCondition(field.conditional, values);
  };

  // Count visible fields in a section
  const countVisibleFields = (section: any): number => {
    return section.fields.filter((field: any) => isFieldVisible(field)).length;
  };

  // Count completed fields in a section
  const countCompletedFields = (section: any): number => {
    return section.fields.filter((field: any) => {
      if (!isFieldVisible(field)) return false;
      const value = values[field.id];
      return value !== undefined && value !== '' && value !== null;
    }).length;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading form...</span>
      </div>
    );
  }

  if (!formData || !formData.sections) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">No form data available</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {formData.sections.map((section: any, sectionIndex: number) => {
        const visibleFieldsCount = countVisibleFields(section);
        const completedFieldsCount = countCompletedFields(section);
        const isExpanded = expandedSections.has(section.id);
        const hasVisibleFields = visibleFieldsCount > 0;

        if (!hasVisibleFields) return null;

        return (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: sectionIndex * 0.1 }}
            className="bg-white border border-gray-200 rounded-lg overflow-hidden"
          >
            {/* Section header */}
            <button
              type="button"
              onClick={() => toggleSection(section.id)}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <span className="text-lg font-semibold text-gray-900">
                  {section.title}
                </span>
                <span className="text-sm text-gray-500">
                  {completedFieldsCount} of {visibleFieldsCount} completed
                </span>
              </div>
              
              <div className="flex items-center gap-3">
                {/* Progress indicator */}
                <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-600 transition-all duration-300"
                    style={{
                      width: `${(completedFieldsCount / visibleFieldsCount) * 100}%`,
                    }}
                  />
                </div>
                
                {isExpanded ? (
                  <ChevronUp className="h-5 w-5 text-gray-400" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-400" />
                )}
              </div>
            </button>

            {/* Section content */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: 'auto' }}
                  exit={{ height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 py-4 space-y-6 border-t border-gray-200">
                    {section.fields.map((field: any) => {
                      if (!isFieldVisible(field)) return null;

                      return (
                        <FormField
                          key={field.id}
                          field={field}
                          value={values[field.id]}
                          onChange={(value) => handleFieldChange(field.id, value)}
                          error={touchedFields.has(field.id) ? errors[field.id] : undefined}
                          touched={touchedFields.has(field.id)}
                        />
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}

      {/* State-specific disclosures */}
      {formData.stateDisclosures && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-6"
        >
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">
                {formData.stateName} Disclosure Requirements
              </h3>
              <div className="text-sm text-blue-800 space-y-2">
                {formData.stateDisclosures.map((disclosure: string, index: number) => (
                  <p key={index}>{disclosure}</p>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}