'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Sparkles } from 'lucide-react';
import { AddressAutocomplete } from './AddressAutocomplete';
import { PropertyDetails } from './PropertyDetails';
import { DynamicFormEngine } from './DynamicFormEngine';
import { RequiredForms } from './RequiredForms';
import { ProgressBar } from './ProgressBar';
import { useStateForm } from '@/hooks/useStateForm';
import { cn } from '@/lib/utils';
import { generateSampleData, generatePropertyTypeSampleData } from '@/lib/sampleDataGenerator';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface MultiStepFormProps {
  className?: string;
}

export function MultiStepForm({ className }: MultiStepFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedState, setSelectedState] = useState<string | null>(null);
  const [propertyAddress, setPropertyAddress] = useState('');
  const [propertyDetails, setPropertyDetails] = useState<any>(null);
  const [isLoadingProperty, setIsLoadingProperty] = useState(false);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isAssignable, setIsAssignable] = useState(false);
  const [buyerType, setBuyerType] = useState('');
  
  const { stateFormData, loadStateForm, isLoading: isLoadingForm } = useStateForm();

  // Update formData when assignable or buyer type changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      isAssignable,
      buyerType
    }));
  }, [isAssignable, buyerType]);

  // Steps configuration
  const steps = [
    { id: 'address', title: 'Property Address', icon: '📍' },
    { id: 'details', title: 'Property Details', icon: '🏠' },
    { id: 'forms', title: 'Required Forms', icon: '📋' },
    { id: 'agreement', title: 'Purchase Agreement', icon: '📄' },
    { id: 'review', title: 'Review & Submit', icon: '✓' },
  ];

  // Handle state detection from address
  const handleStateDetected = async (state: string) => {
    if (state !== selectedState) {
      setSelectedState(state);
      await loadStateForm(state);
    }
  };

  // Fetch property details
  const fetchPropertyDetails = async (address: string) => {
    setIsLoadingProperty(true);
    try {
      const response = await fetch('/api/property/details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ address }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch property details');
      }

      const data = await response.json();
      setPropertyDetails(data);
      
      // Pre-fill form data with property details
      setFormData((prev) => ({
        ...prev,
        property_address: address,
        property_type: data.property_type?.toLowerCase() || 'single_family',
        beds: data.beds,
        baths: data.baths,
        sqft: data.sqft,
        year_built: data.year_built,
        lot_size: data.lot_size,
        parcel_number: data.parcel_number,
        legal_description: data.legal_description,
      }));
    } catch (error) {
      console.error('Error fetching property details:', error);
    } finally {
      setIsLoadingProperty(false);
    }
  };

  // Handle address selection
  const handleAddressChange = async (address: string, suggestion?: any) => {
    setPropertyAddress(address);
    
    if (suggestion) {
      // Fetch property details when a suggestion is selected
      await fetchPropertyDetails(address);
    }
  };

  // Navigation handlers
  const canGoNext = () => {
    switch (currentStep) {
      case 0:
        return propertyAddress && selectedState;
      case 1:
        return propertyDetails !== null;
      case 2:
        return true; // Form validation handled internally
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (canGoNext() && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Update form data
  const handleFormDataChange = (data: Record<string, any>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  };

  // Step content renderer
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                What's the property address?
              </h2>
              <p className="text-gray-600">
                Start by entering the address of the property you want to purchase.
                We'll automatically detect the state and load the appropriate forms.
              </p>
            </div>
            
            <AddressAutocomplete
              value={propertyAddress}
              onChange={handleAddressChange}
              onStateDetected={handleStateDetected}
              placeholder="Enter property address..."
              autoFocus
              required
              className="mt-6"
            />
            
            {/* Additional Parameters */}
            <div className="mt-6 space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <Label htmlFor="assignable" className="text-sm font-medium">
                  Assignable Contract
                </Label>
                <Switch
                  id="assignable"
                  checked={isAssignable}
                  onCheckedChange={setIsAssignable}
                />
              </div>
              
              <div>
                <Label htmlFor="buyerType" className="text-sm font-medium mb-2 block">
                  Buyer Type
                </Label>
                <Select value={buyerType} onValueChange={setBuyerType}>
                  <SelectTrigger id="buyerType" className="w-full">
                    <SelectValue placeholder="Select buyer type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wholesalers">Wholesalers</SelectItem>
                    <SelectItem value="section8">Section 8 Investors</SelectItem>
                    <SelectItem value="direct">Direct Buyers</SelectItem>
                    <SelectItem value="na">N/A</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {selectedState && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6"
              >
                <RequiredForms
                  state={selectedState}
                  propertyType={propertyDetails?.property_type || 'SINGLE_FAMILY'}
                  yearBuilt={propertyDetails?.year_built}
                />
              </motion.div>
            )}
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Property Details
              </h2>
              <p className="text-gray-600">
                Review the property information we found. You can edit any details if needed.
              </p>
            </div>
            
            <PropertyDetails
              address={propertyAddress}
              details={propertyDetails}
              isLoading={isLoadingProperty}
              onDetailsChange={(details) => setPropertyDetails(details)}
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Purchase Agreement Details
                </h2>
                <p className="text-gray-600">
                  Complete the purchase agreement form for {selectedState}.
                </p>
              </div>
              <button
                onClick={() => {
                  const sampleData = propertyDetails?.type 
                    ? generatePropertyTypeSampleData(selectedState || 'FL', propertyDetails.type)
                    : generateSampleData(selectedState || 'FL');
                  
                  // Merge with existing data to preserve any user-entered values
                  setFormData(prevData => ({
                    ...sampleData,
                    ...prevData,
                    // Always prefill these fields from sample data
                    ...Object.keys(sampleData).reduce((acc, key) => {
                      if (!prevData[key] || prevData[key] === '') {
                        acc[key] = sampleData[key];
                      }
                      return acc;
                    }, {} as Record<string, any>)
                  }));
                }}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Sparkles className="w-4 h-4" />
                Prefill with Sample Data
              </button>
            </div>
            
            {stateFormData && (
              <DynamicFormEngine
                formData={stateFormData}
                values={formData}
                onChange={handleFormDataChange}
                isLoading={isLoadingForm}
              />
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Review Your Agreement
              </h2>
              <p className="text-gray-600">
                Please review all the information before submitting.
              </p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-6">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(formData, null, 2)}
              </pre>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn('max-w-4xl mx-auto', className)}>
      {/* Progress bar */}
      <ProgressBar
        steps={steps}
        currentStep={currentStep}
        className="mb-8"
      />
      
      {/* Form content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </div>
        
        {/* Navigation buttons */}
        <div className="border-t border-gray-200 px-8 py-4">
          <div className="flex justify-between">
            <button
              type="button"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className={cn(
                'inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg',
                'transition-colors duration-200',
                currentStep === 0
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-100'
              )}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </button>
            
            <button
              type="button"
              onClick={handleNext}
              disabled={!canGoNext() || currentStep === steps.length - 1}
              className={cn(
                'inline-flex items-center gap-2 px-6 py-2 text-sm font-medium rounded-lg',
                'transition-colors duration-200',
                canGoNext() && currentStep < steps.length - 1
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              )}
            >
              {currentStep === steps.length - 1 ? 'Submit' : 'Next'}
              {currentStep < steps.length - 1 && <ChevronRight className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}