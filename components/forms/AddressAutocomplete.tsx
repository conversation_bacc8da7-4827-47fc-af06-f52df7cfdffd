'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MapP<PERSON>, ArrowR<PERSON>, Loader2 } from 'lucide-react';
import { fetchAddressSuggestions, fetchPropertyDetails, debounce, type AddressSuggestion, type PropertyDetail } from '@/lib/property-api';

interface AddressAutocompleteProps {
  onAddressSelect: (address: string, propertyDetails?: PropertyDetail) => void;
  placeholder?: string;
  className?: string;
}

export function AddressAutocomplete({
  onAddressSelect,
  placeholder = "123 Main Street, Miami, FL 33101",
  className = ""
}: AddressAutocompleteProps) {
  const [addressInput, setAddressInput] = useState('');
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounced fetch function
  const debouncedFetch = debounce(async (searchValue: string) => {
    if (searchValue.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const results = await fetchAddressSuggestions(searchValue);
      setSuggestions(results);
      setShowSuggestions(results.length > 0);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setIsLoading(false);
    }
  }, 300);

  // Handle input changes
  useEffect(() => {
    if (addressInput.trim()) {
      debouncedFetch(addressInput);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
      setIsLoading(false);
    }
  }, [addressInput]);

  // Handle address selection
  const handleAddressSelect = async (selectedAddress: string) => {
    setAddressInput(selectedAddress);
    setShowSuggestions(false);
    setIsLoadingDetails(true);

    try {
      const propertyDetails = await fetchPropertyDetails(selectedAddress);
      onAddressSelect(selectedAddress, propertyDetails || undefined);
    } catch (error) {
      console.error('Error fetching property details:', error);
      onAddressSelect(selectedAddress);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    if (addressInput.trim()) {
      handleAddressSelect(addressInput);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={addressInput}
          onChange={(e) => setAddressInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
          disabled={isLoadingDetails}
          className="w-full pl-4 pr-4 py-4 text-lg border-2 border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-200 bg-white focus:bg-white shadow-sm disabled:opacity-50 text-slate-800 placeholder-slate-500 font-medium"
        />
        
        {/* Loading indicator for property details */}
        {isLoadingDetails && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
          </div>
        )}
      </div>
      
      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-slate-300 rounded-lg shadow-2xl p-2 z-50">
          <div className="px-4 py-3 bg-slate-50 border-b border-slate-200 rounded-t-lg">
            <div className="flex items-center gap-2 text-slate-700 text-sm font-semibold">
              <MapPin className="h-4 w-4 text-amber-600" />
              <span>Select Property Address ({suggestions.length} found)</span>
              {isLoading && <Loader2 className="h-4 w-4 animate-spin ml-2 text-amber-600" />}
            </div>
          </div>
          <div className="space-y-0 max-h-96 overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion.id || index}
                onClick={() => handleAddressSelect(suggestion.full_address)}
                disabled={isLoadingDetails}
                className="w-full px-5 py-4 text-left transition-all duration-200 border-b border-slate-200 last:border-b-0 flex items-center gap-3 group focus:outline-none focus:ring-2 focus:ring-amber-400 bg-white hover:bg-amber-50 active:bg-amber-100 disabled:opacity-50 disabled:cursor-not-allowed last:rounded-b-lg"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-slate-100 group-hover:bg-amber-100 rounded-lg flex items-center justify-center transition-colors">
                  <MapPin className="h-4 w-4 text-slate-500 group-hover:text-amber-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-slate-800 font-semibold truncate">{suggestion.full_address}</div>
                  <div className="text-xs text-slate-500 mt-1">Select this property address</div>
                </div>
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <ArrowRight className="h-4 w-4 text-amber-600" />
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Submit button */}
      <button
        onClick={handleSubmit}
        disabled={!addressInput.trim() || isLoadingDetails}
        className="w-full mt-6 flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-bold rounded-lg hover:from-amber-700 hover:to-yellow-700 focus:ring-4 focus:ring-amber-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 group"
      >
        {isLoadingDetails ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            Retrieving Property Data...
          </>
        ) : (
          <>
            Begin Purchase Agreement
            <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </>
        )}
      </button>
    </div>
  );
}