'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Info } from 'lucide-react';

interface FormFieldProps {
  field: any;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  touched?: boolean;
}

export function FormField({ field, value, onChange, error, touched }: FormFieldProps) {
  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            id={field.id}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.placeholder}
            className={cn(error && 'border-red-500')}
          />
        );
      
      case 'textarea':
        return (
          <Textarea
            id={field.id}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.placeholder}
            rows={field.rows || 4}
            className={cn(error && 'border-red-500')}
          />
        );
      
      case 'select':
        return (
          <Select value={value || ''} onValueChange={handleChange}>
            <SelectTrigger id={field.id} className={cn(error && 'border-red-500')}>
              <SelectValue placeholder={field.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.id}
              checked={!!value}
              onCheckedChange={handleChange}
              className={cn(error && 'border-red-500')}
            />
            <label
              htmlFor={field.id}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {field.checkboxLabel || field.label}
            </label>
          </div>
        );
      
      case 'radio':
        return (
          <RadioGroup value={value || ''} onValueChange={handleChange}>
            {field.options?.map((option: any) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`${field.id}-${option.value}`} />
                <Label htmlFor={`${field.id}-${option.value}`}>{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      
      case 'date':
        return (
          <Input
            id={field.id}
            type="date"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className={cn(error && 'border-red-500')}
          />
        );
      
      case 'currency':
        return (
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
            <Input
              id={field.id}
              type="number"
              value={value || ''}
              onChange={(e) => handleChange(e.target.value)}
              className={cn('pl-7', error && 'border-red-500')}
              placeholder={field.placeholder}
              min={field.min}
              max={field.max}
            />
          </div>
        );
      
      case 'number':
        return (
          <Input
            id={field.id}
            type="number"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className={cn(error && 'border-red-500')}
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
          />
        );
      
      case 'info':
        return (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-700">{field.helpText || field.label}</div>
          </div>
        );
      
      default:
        return (
          <Input
            id={field.id}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.placeholder}
            className={cn(error && 'border-red-500')}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      {field.type !== 'checkbox' && field.type !== 'info' && (
        <Label 
          htmlFor={field.id} 
          className={cn(
            "block text-sm font-medium",
            field.required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ""
          )}
        >
          {field.label}
        </Label>
      )}
      
      {renderField()}
      
      {field.helpText && field.type !== 'info' && (
        <p className="text-sm text-gray-500 mt-1">{field.helpText}</p>
      )}
      
      {touched && error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
    </div>
  );
}