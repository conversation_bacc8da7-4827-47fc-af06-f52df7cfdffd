'use client';

import React from 'react';
import { Home, Bed, Bath, Square, Calendar, MapPin, DollarSign } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PropertyDetailsProps {
  address: string;
  details: any;
  isLoading: boolean;
  onDetailsChange?: (details: any) => void;
  className?: string;
}

export function PropertyDetails({
  address,
  details,
  isLoading,
  onDetailsChange,
  className,
}: PropertyDetailsProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-3 text-gray-600">Loading property details...</span>
      </div>
    );
  }

  if (!details) {
    return (
      <div className="text-center py-12">
        <Home className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">No property details available</p>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Property address */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
          <div>
            <h3 className="font-semibold text-gray-900">{address}</h3>
            {details.county && (
              <p className="text-sm text-gray-600">{details.county} County</p>
            )}
          </div>
        </div>
      </div>

      {/* Property characteristics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-1">
            <Bed className="h-4 w-4" />
            <span className="text-sm">Bedrooms</span>
          </div>
          <p className="text-2xl font-semibold text-gray-900">
            {details.beds || '—'}
          </p>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-1">
            <Bath className="h-4 w-4" />
            <span className="text-sm">Bathrooms</span>
          </div>
          <p className="text-2xl font-semibold text-gray-900">
            {details.baths || '—'}
          </p>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-1">
            <Square className="h-4 w-4" />
            <span className="text-sm">Square Feet</span>
          </div>
          <p className="text-2xl font-semibold text-gray-900">
            {details.sqft ? formatNumber(details.sqft) : '—'}
          </p>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-1">
            <Calendar className="h-4 w-4" />
            <span className="text-sm">Year Built</span>
          </div>
          <p className="text-2xl font-semibold text-gray-900">
            {details.year_built || '—'}
          </p>
        </div>
      </div>

      {/* Property value and tax info */}
      {(details.estimated_value || details.tax_assessed_value) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {details.estimated_value && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-700 mb-1">
                <DollarSign className="h-4 w-4" />
                <span className="text-sm font-medium">Estimated Value</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                {formatCurrency(details.estimated_value)}
              </p>
            </div>
          )}

          {details.tax_assessed_value && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-gray-600 mb-1">
                <DollarSign className="h-4 w-4" />
                <span className="text-sm">Tax Assessed Value</span>
              </div>
              <p className="text-xl font-semibold text-gray-900">
                {formatCurrency(details.tax_assessed_value)}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Additional details */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 mb-4">Additional Details</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 mb-1">Property Type</p>
            <p className="font-medium text-gray-900">
              {details.property_type || 'Single Family'}
            </p>
          </div>

          {details.lot_size && (
            <div>
              <p className="text-sm text-gray-600 mb-1">Lot Size</p>
              <p className="font-medium text-gray-900">
                {formatNumber(details.lot_size)} sq ft
              </p>
            </div>
          )}

          {details.parcel_number && (
            <div>
              <p className="text-sm text-gray-600 mb-1">Parcel Number</p>
              <p className="font-medium text-gray-900">{details.parcel_number}</p>
            </div>
          )}

          {details.subdivision && (
            <div>
              <p className="text-sm text-gray-600 mb-1">Subdivision</p>
              <p className="font-medium text-gray-900">{details.subdivision}</p>
            </div>
          )}
        </div>

        {details.legal_description && (
          <div className="mt-4">
            <p className="text-sm text-gray-600 mb-1">Legal Description</p>
            <p className="font-medium text-gray-900 text-sm">
              {details.legal_description}
            </p>
          </div>
        )}
      </div>

      {/* Owner information */}
      {details.owner_name && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">Current Owner</h4>
          <p className="text-gray-700">{details.owner_name}</p>
          {details.owner_address && (
            <p className="text-sm text-gray-600 mt-1">{details.owner_address}</p>
          )}
        </div>
      )}
    </div>
  );
}