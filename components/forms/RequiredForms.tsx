'use client';

import React from 'react';
import { FileText, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RequiredForm, PropertyType } from '@/app/types/index';
import { getRequiredForms, getFormsByAgreementType, getStateName } from '@/lib/test';

interface RequiredFormsProps {
  state: string;
  propertyType?: PropertyType;
  yearBuilt?: number;
  className?: string;
}

export function RequiredForms({
  state,
  propertyType = 'SINGLE_FAMILY',
  yearBuilt,
  className
}: RequiredFormsProps) {
  const allForms = getRequiredForms(state, propertyType, yearBuilt);
  const requiredForms = allForms.filter(form => form.required);
  const optionalForms = allForms.filter(form => !form.required);
  const stateName = getStateName(state);

  const getFormIcon = (form: RequiredForm) => {
    if (form.required) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    return <Info className="h-4 w-4 text-blue-500" />;
  };

  const getFormBadge = (form: RequiredForm) => {
    if (form.required) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Required
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        Optional
      </span>
    );
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Required Forms for {stateName}
        </h2>
        <p className="text-gray-600">
          Based on your property type ({propertyType.replace('_', ' ').toLowerCase()}) 
          {yearBuilt && ` built in ${yearBuilt}`}
        </p>
      </div>

      {/* Property Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-900 mb-1">
              Property Information
            </h3>
            <p className="text-sm text-blue-700">
              All required and recommended forms for your {propertyType.replace('_', ' ').toLowerCase()} property in {stateName}.
            </p>
          </div>
        </div>
      </div>

      {/* Required Forms */}
      {requiredForms.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Required Forms ({requiredForms.length})
          </h3>
          <div className="space-y-3">
            {requiredForms.map((form) => (
              <div
                key={form.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-start justify-between gap-4">
                  <div className="flex items-start gap-3 flex-1">
                    <FileText className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">{form.name}</h4>
                        {getFormBadge(form)}
                      </div>
                      <p className="text-sm text-gray-600">{form.description}</p>
                    </div>
                  </div>
                  {getFormIcon(form)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Optional Forms */}
      {optionalForms.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-500" />
            Optional Forms ({optionalForms.length})
          </h3>
          <div className="space-y-3">
            {optionalForms.map((form) => (
              <div
                key={form.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-start justify-between gap-4">
                  <div className="flex items-start gap-3 flex-1">
                    <FileText className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">{form.name}</h4>
                        {getFormBadge(form)}
                      </div>
                      <p className="text-sm text-gray-600">{form.description}</p>
                    </div>
                  </div>
                  {getFormIcon(form)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <h3 className="font-medium text-gray-900">Form Summary</h3>
        </div>
        <div className="text-sm text-gray-600 space-y-1">
          <p>• {requiredForms.length} required forms</p>
          <p>• {optionalForms.length} optional forms</p>
          <p>• Total: {allForms.length} forms for {stateName}</p>
        </div>
      </div>
    </div>
  );
}
