'use client';

import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Step {
  id: string;
  title: string;
  icon?: string;
}

interface ProgressBarProps {
  steps: Step[];
  currentStep: number;
  className?: string;
}

export function ProgressBar({ steps, currentStep, className }: ProgressBarProps) {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;
          const isLast = index === steps.length - 1;

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    'flex h-10 w-10 items-center justify-center rounded-full',
                    'border-2 transition-all duration-300',
                    isCompleted
                      ? 'border-blue-600 bg-blue-600 text-white'
                      : isActive
                      ? 'border-blue-600 bg-white text-blue-600'
                      : 'border-gray-300 bg-white text-gray-400'
                  )}
                >
                  {isCompleted ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    <span className="text-sm font-semibold">
                      {step.icon || index + 1}
                    </span>
                  )}
                </div>
                
                <span
                  className={cn(
                    'mt-2 text-sm font-medium',
                    isActive ? 'text-blue-600' : 'text-gray-500'
                  )}
                >
                  {step.title}
                </span>
              </div>

              {!isLast && (
                <div className="flex-1 px-4">
                  <div className="relative h-0.5 bg-gray-300">
                    <div
                      className={cn(
                        'absolute left-0 top-0 h-full bg-blue-600 transition-all duration-500',
                        isCompleted ? 'w-full' : 'w-0'
                      )}
                    />
                  </div>
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}