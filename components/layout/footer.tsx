import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-slate-800 text-slate-300 border-t border-amber-600/20">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 bg-amber-600 rounded-full"></div>
                <h3 className="text-2xl font-bold text-white font-serif">PropBolt</h3>
              </div>
              <p className="text-slate-400 mb-6 leading-relaxed max-w-md">
                Create professional real estate purchase agreements in minutes.
                State-compliant documentation with smart data integration.
              </p>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-600/10 border border-amber-600/30 rounded-lg text-amber-300 text-sm font-semibold">
                <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                <span>Built by Licensed Brokers</span>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h4 className="text-sm font-bold text-white uppercase mb-4 tracking-wide">Product</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="#features" className="text-slate-400 hover:text-amber-400 transition-colors">Features</Link></li>
                <li><Link href="#pricing" className="text-slate-400 hover:text-amber-400 transition-colors">Pricing</Link></li>
                <li><Link href="#states" className="text-slate-400 hover:text-amber-400 transition-colors">Supported States</Link></li>
              </ul>
            </div>

            {/* Legal Links */}
            <div>
              <h4 className="text-sm font-bold text-white uppercase mb-4 tracking-wide">Legal</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="/privacy" className="text-slate-400 hover:text-amber-400 transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-slate-400 hover:text-amber-400 transition-colors">Terms of Service</Link></li>
                <li><Link href="/disclaimer" className="text-slate-400 hover:text-amber-400 transition-colors">Disclaimer</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-slate-700">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-sm text-slate-500">
                © {new Date().getFullYear()} PropBolt. All rights reserved.
              </p>
              <p className="text-xs text-slate-500 text-center md:text-right max-w-md">
                PropBolt is a documentation service only. We do not provide legal advice.
                <br />Consult a licensed attorney before finalizing any legal documents.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}