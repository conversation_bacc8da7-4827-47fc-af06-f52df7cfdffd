"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Logo } from "@/components/ui/logo"
import { Menu, X } from "lucide-react"
import { useState } from "react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navLinks = [
    { href: "/create", label: "Create Agreement" },
    { href: "/master-class", label: "Master Class" },
    { href: "/help", label: "Help" },
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b border-amber-600/20 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="flex items-center space-x-2">
              <Logo width={160} height={38} />
            </Link>
            
            <nav className="hidden md:flex items-center gap-6">
              {/* Show regular nav links */}
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="text-sm font-semibold text-slate-700 hover:text-amber-600 transition-colors"
                >
                  {link.label}
                </Link>
              ))}
            </nav>
          </div>

          <div className="flex items-center gap-4">
            <Link href="/login" className="text-sm font-semibold text-slate-700 hover:text-amber-600 transition-colors">
              Log In
            </Link>
            <Link href="/signup" className="hidden sm:inline-flex">
              <Button size="sm" className="bg-gradient-to-r from-amber-600 to-yellow-600 hover:from-amber-700 hover:to-yellow-700 text-white border-0 font-semibold">
                Get Started
              </Button>
            </Link>

            <button
              className="inline-flex items-center justify-center rounded-md p-2 text-slate-700 hover:bg-slate-100 md:hidden transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-slate-200 bg-white/95 backdrop-blur-sm">
            <nav className="flex flex-col gap-4">
              {/* Show regular nav links for mobile */}
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="text-sm font-semibold text-slate-700 hover:text-amber-600 transition-colors px-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}

              <Link
                href="/contact"
                className="text-sm font-semibold text-slate-700 hover:text-amber-600 transition-colors px-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact us
              </Link>
              <Link href="/signup" className="px-2" onClick={() => setIsMenuOpen(false)}>
                <Button className="w-full bg-gradient-to-r from-amber-600 to-yellow-600 hover:from-amber-700 hover:to-yellow-700 text-white border-0 font-semibold" size="sm">
                  Get Started
                </Button>
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}