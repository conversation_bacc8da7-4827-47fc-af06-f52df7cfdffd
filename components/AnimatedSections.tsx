"use client";

import React, { useState, useEffect } from 'react';
import { MapPin, Building, FileText, CheckCircle, Clock, Shield, Zap, TrendingUp, DollarSign, Users, Home, Search, ArrowRight, Play, Pause } from 'lucide-react';

interface AnimatedSectionsProps {
  onAddressInputFocus: () => void;
}

export function AnimatedSections({ onAddressInputFocus }: AnimatedSectionsProps) {
  const [activeSection, setActiveSection] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [countdown, setCountdown] = useState(5);
  const [propertyData, setPropertyData] = useState({
    address: '',
    owner: '',
    value: '',
    legalDescription: '',
    taxId: '',
    county: ''
  });

  const sections = [
    {
      id: 'live-intelligence',
      title: 'Live Property Intelligence',
      subtitle: 'Watch real property data populate instantly'
    },
    {
      id: 'smart-forms',
      title: 'Smart Form Pre-Population',
      subtitle: 'See how your actual forms get filled automatically'
    },
    {
      id: 'florida-requirements',
      title: 'Florida Requirements Generator',
      subtitle: 'Dynamic form requirements based on your property'
    }
  ];

  // Auto-rotation logic
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setActiveSection(current => (current + 1) % sections.length);
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isPlaying, sections.length]);

  // Animated typing effect for property data
  useEffect(() => {
    if (activeSection === 0) {
      const demoData = {
        address: '742 Evergreen Terrace, Orlando, FL 32801',
        owner: 'John & Mary Simpson',
        value: '$289,000',
        legalDescription: 'LOT 12, BLOCK 5, EVERGREEN ESTATES',
        taxId: '14-28-126-005',
        county: 'Orange County'
      };

      const animateTyping = (key: keyof typeof demoData, text: string, delay: number) => {
        setTimeout(() => {
          setPropertyData(prev => ({ ...prev, [key]: '' }));
          let i = 0;
          const typeInterval = setInterval(() => {
            if (i < text.length) {
              setPropertyData(prev => ({ ...prev, [key]: text.slice(0, i + 1) }));
              i++;
            } else {
              clearInterval(typeInterval);
            }
          }, 50);
        }, delay);
      };

      // Reset and animate
      setPropertyData({
        address: '',
        owner: '',
        value: '',
        legalDescription: '',
        taxId: '',
        county: ''
      });

      animateTyping('address', demoData.address, 500);
      animateTyping('owner', demoData.owner, 1500);
      animateTyping('value', demoData.value, 2500);
      animateTyping('legalDescription', demoData.legalDescription, 3500);
      animateTyping('taxId', demoData.taxId, 4500);
      animateTyping('county', demoData.county, 5500);
    }
  }, [activeSection]);

  const handleSectionClick = (index: number) => {
    setActiveSection(index);
    setCountdown(5);
    setIsPlaying(false);
    setTimeout(() => setIsPlaying(true), 1000);
  };

  return (
    <div className="max-w-6xl mx-auto mb-8">
      {/* Section Navigation */}
      <div className="flex justify-center items-center gap-4 mb-6">
        {sections.map((section, index) => (
          <button
            key={section.id}
            onClick={() => handleSectionClick(index)}
            className={`px-4 py-2 rounded-xl font-semibold transition-all duration-300 shadow-lg text-sm ${
              activeSection === index
                ? "bg-[#006AFF] text-white transform scale-105"
                : "bg-white text-[#006AFF] hover:bg-blue-50 border-2 border-[#006AFF]"
            }`}
          >
            {section.title}
          </button>
        ))}
        
        {/* Play/Pause Control */}
        <button
          onClick={() => setIsPlaying(!isPlaying)}
          className="p-2 rounded-full bg-white border-2 border-[#006AFF] text-[#006AFF] hover:bg-blue-50 transition-colors"
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </button>
      </div>

      {/* Current Section Display */}
      <div className="text-center mb-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{sections[activeSection].title}</h2>
        <p className="text-gray-600">{sections[activeSection].subtitle}</p>
      </div>

      {/* Animated Content */}
      <div className="min-h-[500px] relative">
        {/* Section 1: Live Property Intelligence */}
        {activeSection === 0 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Side - Address Input Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Smart Address Search</h3>
                <div className="relative">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                    placeholder="Start typing address..."
                    readOnly
                  />
                </div>
                
                {propertyData.address && (
                  <div className="bg-blue-50 rounded-xl p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-2 text-[#006AFF] text-sm font-medium mb-2">
                      <MapPin className="h-4 w-4" />
                      <span>Property Found!</span>
                    </div>
                    <p className="text-sm text-gray-600">PropBolt API retrieving details...</p>
                  </div>
                )}
              </div>

              {/* Right Side - Property Data Cards */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Instant Property Data</h3>
                
                {/* Owner Information */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.owner ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-1">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Owner Name</span>
                    {propertyData.owner && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                  <p className="text-sm text-gray-900 font-mono">
                    {propertyData.owner || 'Loading...'}
                  </p>
                </div>

                {/* Property Value */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.value ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-1">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Estimated Value</span>
                    {propertyData.value && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                  <p className="text-sm text-gray-900 font-mono">
                    {propertyData.value || 'Loading...'}
                  </p>
                </div>

                {/* Legal Description */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.legalDescription ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-1">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Legal Description</span>
                    {propertyData.legalDescription && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                  <p className="text-sm text-gray-900 font-mono">
                    {propertyData.legalDescription || 'Loading...'}
                  </p>
                </div>

                {/* Tax ID */}
                <div className={`p-4 rounded-lg border-2 transition-all duration-500 ${
                  propertyData.taxId ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-1">
                    <Building className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Property Tax ID</span>
                    {propertyData.taxId && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                  <p className="text-sm text-gray-900 font-mono">
                    {propertyData.taxId || 'Loading...'}
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 text-center">
              <button
                onClick={onAddressInputFocus}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-[#006AFF] to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <MapPin className="h-5 w-5" />
                Try It Now - Enter Your Address
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* Section 2: Smart Form Pre-Population */}
        {activeSection === 1 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Side - Form Fields */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📝 Actual Form Fields</h3>
                
                {/* Seller Name Field */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Seller Name(s)</label>
                  <input
                    type="text"
                    value={propertyData.owner}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                    placeholder="Auto-populating from PropBolt..."
                    readOnly
                  />
                </div>

                {/* Property Address */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Property Address</label>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                    placeholder="Auto-populating from PropBolt..."
                    readOnly
                  />
                </div>

                {/* County */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">County</label>
                  <input
                    type="text"
                    value={propertyData.county}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                    placeholder="Auto-populating from PropBolt..."
                    readOnly
                  />
                </div>

                {/* Legal Description */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Legal Description</label>
                  <textarea
                    value={propertyData.legalDescription}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all h-20 resize-none"
                    placeholder="Auto-populating from PropBolt..."
                    readOnly
                  />
                </div>
              </div>

              {/* Right Side - Progress & Confidence */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🎯 Pre-Population Progress</h3>
                
                {/* Progress Bars */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Owner Information</span>
                      <span className="text-[#006AFF] font-medium">
                        {propertyData.owner ? '100%' : '0%'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#006AFF] h-2 rounded-full transition-all duration-1000"
                        style={{ width: propertyData.owner ? '100%' : '0%' }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Property Details</span>
                      <span className="text-[#006AFF] font-medium">
                        {propertyData.address ? '100%' : '0%'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#006AFF] h-2 rounded-full transition-all duration-1000"
                        style={{ width: propertyData.address ? '100%' : '0%' }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Legal Information</span>
                      <span className="text-[#006AFF] font-medium">
                        {propertyData.legalDescription ? '100%' : '0%'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#006AFF] h-2 rounded-full transition-all duration-1000"
                        style={{ width: propertyData.legalDescription ? '100%' : '0%' }}
                      />
                    </div>
                  </div>
                </div>

                {/* Confidence Score */}
                <div className="bg-green-50 rounded-xl p-4 border-2 border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-green-800">Data Confidence Score</span>
                  </div>
                  <div className="text-3xl font-bold text-green-800">
                    {propertyData.owner && propertyData.address && propertyData.legalDescription ? '98%' : '0%'}
                  </div>
                  <p className="text-sm text-green-700">
                    {propertyData.owner && propertyData.address && propertyData.legalDescription 
                      ? 'Excellent data quality from PropBolt API' 
                      : 'Waiting for property data...'}
                  </p>
                </div>

                {/* Time Saved */}
                <div className="bg-blue-50 rounded-xl p-4 border-2 border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <span className="font-semibold text-blue-800">Time Saved</span>
                  </div>
                  <div className="text-3xl font-bold text-blue-800">
                    {propertyData.owner && propertyData.address && propertyData.legalDescription ? '45 min' : '0 min'}
                  </div>
                  <p className="text-sm text-blue-700">
                    vs. manual data entry
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 text-center">
              <button
                onClick={onAddressInputFocus}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-[#006AFF] to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <FileText className="h-5 w-5" />
                Experience Smart Pre-Population
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* Section 3: Florida Requirements Generator */}
        {activeSection === 2 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Side - Property Analysis */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🏠 Property Analysis</h3>
                
                {/* Property Characteristics */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">State</span>
                    <span className="text-sm text-[#006AFF] font-semibold">Florida</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">Property Type</span>
                    <span className="text-sm text-[#006AFF] font-semibold">Single Family</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">Year Built</span>
                    <span className="text-sm text-[#006AFF] font-semibold">1965</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">HOA</span>
                    <span className="text-sm text-orange-600 font-semibold">Detected</span>
                  </div>
                </div>

                {/* Florida Map Visual */}
                <div className="bg-blue-50 rounded-xl p-4 text-center">
                  <div className="text-4xl mb-2">🏛️</div>
                  <h4 className="font-semibold text-blue-800">Florida State Requirements</h4>
                  <p className="text-sm text-blue-600">Analyzing property characteristics...</p>
                </div>
              </div>

              {/* Right Side - Required Forms */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📋 Required Forms</h3>
                
                {/* Required Forms List */}
                <div className="space-y-3">
                  {/* Purchase Agreement */}
                  <div className="flex items-center gap-3 p-4 bg-green-50 border-2 border-green-200 rounded-lg animate-in slide-in-from-right-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-800">Florida Purchase Agreement</h4>
                      <p className="text-sm text-green-600">Required for all Florida transactions</p>
                    </div>
                    <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">REQUIRED</span>
                  </div>

                  {/* Property Disclosure */}
                  <div className="flex items-center gap-3 p-4 bg-green-50 border-2 border-green-200 rounded-lg animate-in slide-in-from-right-2" style={{ animationDelay: '0.3s' }}>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-800">Florida Property Disclosure</h4>
                      <p className="text-sm text-green-600">State-mandated seller disclosure</p>
                    </div>
                    <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">REQUIRED</span>
                  </div>

                  {/* Lead Paint (conditional) */}
                  <div className="flex items-center gap-3 p-4 bg-red-50 border-2 border-red-200 rounded-lg animate-in slide-in-from-right-2" style={{ animationDelay: '0.6s' }}>
                    <Shield className="h-5 w-5 text-red-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-red-800">Lead Paint Disclosure</h4>
                      <p className="text-sm text-red-600">Required for homes built before 1978</p>
                    </div>
                    <span className="text-xs bg-red-200 text-red-800 px-2 py-1 rounded">REQUIRED</span>
                  </div>

                  {/* HOA Disclosure (optional) */}
                  <div className="flex items-center gap-3 p-4 bg-yellow-50 border-2 border-yellow-200 rounded-lg animate-in slide-in-from-right-2" style={{ animationDelay: '0.9s' }}>
                    <Home className="h-5 w-5 text-yellow-600" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-yellow-800">HOA Disclosure</h4>
                      <p className="text-sm text-yellow-600">HOA detected - disclosure recommended</p>
                    </div>
                    <span className="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded">OPTIONAL</span>
                  </div>
                </div>

                {/* Summary */}
                <div className="bg-blue-50 rounded-xl p-4 border-2 border-blue-200 mt-6">
                  <h4 className="font-semibold text-blue-800 mb-2">Document Package Summary</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Required Forms:</span>
                      <span className="font-semibold">3</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Optional Forms:</span>
                      <span className="font-semibold">1</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Package:</span>
                      <span className="font-semibold">4 documents</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 text-center">
              <button
                onClick={onAddressInputFocus}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-[#006AFF] to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <FileText className="h-5 w-5" />
                Get My Florida Requirements
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* Countdown Timer */}
        {isPlaying && (
          <div className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-[#006AFF] rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">{countdown}</span>
              </div>
              <span className="text-xs text-gray-500">Next section</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}