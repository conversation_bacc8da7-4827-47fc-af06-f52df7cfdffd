"use client";

import React from 'react';
import { ArrowUp, FileText } from 'lucide-react';
import { useScrollPosition } from '@/hooks/useScrollPosition';

interface StickyGetStartedProps {
  onClick: () => void;
}

export function StickyGetStarted({ onClick }: StickyGetStartedProps) {
  const { isScrolledPastHero } = useScrollPosition();

  if (!isScrolledPastHero) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50 animate-in slide-in-from-bottom-4 fade-in duration-500">
      <div className="relative">
        {/* Subtle backdrop blur effect */}
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-xl"></div>

        <button
          onClick={onClick}
          className="relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 border border-amber-500/20"
        >
          <FileText className="h-4 w-4" />
          <span className="text-sm">Start Agreement</span>
          <ArrowUp className="h-4 w-4" />
        </button>

        {/* Professional indicator dot */}
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse border-2 border-white"></div>
      </div>
    </div>
  );
}