"use client";

import { useState } from "react";
import { Mail } from "lucide-react";

export default function EmailCapture() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/subscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        throw new Error("Failed to subscribe");
      }

      setSuccess(true);
      setEmail("");
      setTimeout(() => setSuccess(false), 5000);
    } catch (err) {
      setError("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mb-12 max-w-2xl mx-auto">
      <div className="bg-gradient-to-r from-[#006AFF] to-[#0056CC] rounded-2xl p-8 shadow-xl">
        <div className="text-center mb-6">
          <Mail className="h-12 w-12 text-white mx-auto mb-4" />
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
            Be the First to Access PropBolt
          </h2>
          <p className="text-white/90 text-lg">
            Join our waitlist and get exclusive early access when we launch
          </p>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
            className="flex-1 px-4 py-3 rounded-lg text-gray-900 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-[#006AFF]"
            required
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading || success}
            className="px-6 py-3 bg-white text-[#006AFF] font-semibold rounded-lg hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Subscribing..." : success ? "✓ Subscribed!" : "Get Early Access"}
          </button>
        </form>

        {error && (
          <p className="mt-3 text-sm text-red-200 text-center">{error}</p>
        )}

        {success && (
          <p className="mt-3 text-sm text-green-200 text-center">
            Thank you! We'll notify you when PropBolt launches.
          </p>
        )}
      </div>
    </div>
  );
}