"use client"

import { useEffect, useState } from "react"
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver"

export function CompactJourney() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })
  const [isAnimated, setIsAnimated] = useState(false)

  useEffect(() => {
    if (isIntersecting && !isAnimated) {
      setIsAnimated(true)
    }
  }, [isIntersecting, isAnimated])

  return (
    <div className="max-w-3xl mx-auto -mt-8 mb-12" ref={targetRef}>
      {/* Creative Title with Emoji and Underline */}
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-900 inline-block relative">
          <span className="bg-gradient-to-r from-[#006AFF] to-purple-600 bg-clip-text text-transparent">
            🚀 Why DIY Your Purchase Agreement?
          </span>
          <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#006AFF] to-purple-600 rounded-full transform scale-x-0 transition-transform duration-700 ease-out"
            style={{ transform: isAnimated ? 'scaleX(1)' : 'scaleX(0)' }}
          />
        </h3>
        <p className="text-gray-600 mt-4 text-sm">
          Skip the middleman, save thousands, and take control of your real estate journey ✨
        </p>
      </div>

      {/* Single Line Card Layout */}
      <div className="flex gap-4 overflow-x-auto pb-2 px-2 -mx-2 scrollbar-hide">
        {/* Card 1 - Traditional Way */}
        <div className={`flex-shrink-0 w-64 transform transition-all duration-700 ${
          isAnimated ? 'translate-x-0 opacity-100' : '-translate-x-8 opacity-0'
        }`}
          style={{ transitionDelay: '200ms' }}
        >
          <div className="bg-gradient-to-br from-red-50 to-red-100 border border-red-200 rounded-xl p-4 h-full hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer">
            <div className="text-center">
              <div className="text-red-600 font-bold text-sm mb-2">❌ The Old Way</div>
              <div className="text-red-800 text-xs leading-tight">
                Agents pay Zillow for leads<br/>
                <span className="font-bold text-lg">→ You pay 1-3% commission</span>
              </div>
              <div className="mt-2 text-red-700 text-xs opacity-75">
                That's $3,000-$9,000 on a $300k home!
              </div>
            </div>
          </div>
        </div>

        {/* Card 2 - Our Solution */}
        <div className={`flex-shrink-0 w-64 transform transition-all duration-700 ${
          isAnimated ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
        }`}
          style={{ transitionDelay: '400ms' }}
        >
          <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-xl p-4 h-full hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer">
            <div className="text-center">
              <div className="text-green-600 font-bold text-sm mb-2">✅ Your Way</div>
              <div className="text-green-800 text-xs leading-tight">
                You control everything<br/>
                <span className="font-bold text-lg">→ $0 commission</span>
              </div>
              <div className="mt-2 text-green-700 text-xs opacity-75">
                Keep that money in your pocket!
              </div>
            </div>
          </div>
        </div>

        {/* Card 3 - Service Model */}
        <div className={`flex-shrink-0 w-64 transform transition-all duration-700 ${
          isAnimated ? 'translate-x-0 opacity-100' : 'translate-x-8 opacity-0'
        }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-4 h-full hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer">
            <div className="text-center">
              <div className="text-blue-600 font-bold text-sm mb-2">💎 Simple Pricing</div>
              <div className="text-blue-800 text-xs leading-tight">
                Monthly subscription<br/>
                <span className="font-bold text-lg">→ Unlimited agreements</span>
              </div>
              <div className="mt-2 text-blue-700 text-xs opacity-75">
                Documentation service only
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile scroll indicator */}
      <div className="flex justify-center mt-4 gap-1 md:hidden">
        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
      </div>
    </div>
  )
}