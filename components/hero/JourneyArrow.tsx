"use client"

import { useEffect, useState } from "react"
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver"

export function JourneyArrow() {
  const { targetRef, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isIntersecting) {
      // Start arrow animation after a delay
      const timer = setTimeout(() => {
        setIsAnimating(true)
      }, 500)
      return () => clearTimeout(timer)
    } else {
      setIsAnimating(false)
    }
  }, [isIntersecting])

  return (
    <div className="relative py-8 md:py-12 bg-gradient-to-b from-indigo-100 to-gray-50" ref={targetRef}>
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          {/* Animated Arrow SVG */}
          <div className="relative">
            <svg
              className="w-full h-16 md:h-24 mx-auto"
              viewBox="0 0 400 100"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Curved path */}
              <path
                d="M200 10 Q200 50 200 90"
                stroke="url(#arrowGradient)"
                strokeWidth="3"
                fill="none"
                strokeDasharray="10,5"
                className={`transition-all duration-2000 ${
                  isAnimating ? 'stroke-dashoffset-0' : 'stroke-dashoffset-100'
                }`}
                style={{
                  strokeDashoffset: isAnimating ? '0' : '100',
                  animation: isAnimating ? 'dash 2s linear forwards' : 'none',
                }}
              />
              
              {/* Arrow head */}
              <polygon
                points="200,90 195,85 205,85"
                fill="url(#arrowGradient)"
                className={`transition-all duration-500 delay-1500 ${
                  isAnimating ? 'opacity-100 scale-100' : 'opacity-0 scale-0'
                }`}
              />
              
              {/* Gradient definition */}
              <defs>
                <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#006AFF" />
                  <stop offset="50%" stopColor="#0080FF" />
                  <stop offset="100%" stopColor="#00A0FF" />
                </linearGradient>
              </defs>
            </svg>

            {/* Floating dots animation */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="space-y-2">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-2 h-2 bg-blue-500 rounded-full mx-auto transition-all duration-1000 ${
                      isAnimating
                        ? 'animate-bounce opacity-100'
                        : 'opacity-0'
                    }`}
                    style={{
                      animationDelay: `${i * 200}ms`,
                      animationDuration: '1s',
                    }}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Journey text */}
          <div className={`mt-6 transition-all duration-700 delay-500 ${
            isAnimating ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
          }`}>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Follow Your Real Estate Journey
            </h3>
            <p className="text-gray-600 text-sm max-w-lg mx-auto">
              From understanding the problem to discovering our solution and simple service model
            </p>
          </div>

          {/* Subtle gradient orbs */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className={`absolute top-1/2 left-1/4 w-20 h-20 bg-blue-200 rounded-full blur-xl transition-all duration-2000 ${
              isAnimating ? 'opacity-30 scale-100' : 'opacity-0 scale-0'
            }`} />
            <div className={`absolute top-1/2 right-1/4 w-16 h-16 bg-indigo-200 rounded-full blur-xl transition-all duration-2000 delay-700 ${
              isAnimating ? 'opacity-30 scale-100' : 'opacity-0 scale-0'
            }`} />
          </div>
        </div>
      </div>
    </div>
  )
}