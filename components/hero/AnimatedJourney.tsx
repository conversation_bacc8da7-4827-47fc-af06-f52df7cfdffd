"use client"

import { <PERSON><PERSON>, <PERSON>rk<PERSON>, CheckCircle, <PERSON>ert<PERSON><PERSON>gle, Users } from "lucide-react"

export function AnimatedJourney() {

  return (
    <section className="py-20 bg-white border-t border-[#006AFF]/20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-[#006AFF]/10 border border-[#006AFF]/30 rounded-lg text-[#006AFF] text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>PROFESSIONAL DOCUMENT AUTOMATION</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
              Why Choose Professional Document Automation?
            </h2>
            <p className="text-xl text-slate-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              Discover how licensed real estate professionals are revolutionizing purchase agreements
              with intelligent automation and legal compliance.
            </p>
          </div>

          {/* Problem vs Solution Comparison */}
          <div className="grid lg:grid-cols-2 gap-8 mb-16">
            {/* Traditional Challenges */}
            <div className="bg-red-50 border-2 border-red-200 rounded-lg p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-slate-800 font-serif">Traditional Challenges</h3>
              </div>
              <div className="space-y-4 text-slate-700">
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Manual data entry errors</strong> in complex legal documents</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Time-consuming research</strong> for property information</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>State compliance complexity</strong> across jurisdictions</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Professional liability risks</strong> from documentation errors</span>
                </p>
              </div>
            </div>

            {/* Our Solution */}
            <div className="bg-green-50 border-2 border-green-200 rounded-lg p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-slate-800 font-serif">PropBolt Solution</h3>
              </div>
              <div className="space-y-4 text-slate-700">
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Automated data population</strong> from 150M+ property records</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Instant property intelligence</strong> in under 90 seconds</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Built-in 50-state compliance</strong> engine</span>
                </p>
                <p className="flex items-start gap-3">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Licensed broker reviewed</strong> for professional accuracy</span>
                </p>
              </div>
            </div>
          </div>

          {/* Key Features */}
          <div className="bg-amber-50 border-2 border-amber-200 rounded-lg p-8 mb-16">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-4 mb-4">
                <div className="w-12 h-12 bg-amber-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-slate-800 font-serif">Professional Features</h3>
                <div className="px-4 py-2 bg-green-600 text-white text-xs font-bold rounded-full">
                  LICENSED BROKER BUILT
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center p-6 bg-white rounded-lg border border-amber-200 shadow-sm">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-slate-800 mb-2">Intelligent Autocomplete</h4>
                <p className="text-slate-600 text-sm">Instant data extraction from 150M+ property records</p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg border border-amber-200 shadow-sm">
                <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-slate-800 mb-2">Smart Form Generation</h4>
                <p className="text-slate-600 text-sm">50-state automated form generation & validation</p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg border border-amber-200 shadow-sm">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-slate-800 mb-2">Professional Expertise</h4>
                <p className="text-slate-600 text-sm">Created by real estate professionals for professionals</p>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-slate-50 border border-slate-200 rounded-lg p-8 mb-16">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-2 font-serif">Proven Results</h3>
              <p className="text-slate-600">Real performance data from our professional platform</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center p-6 bg-white rounded-lg border border-slate-200">
                <div className="text-3xl font-bold text-amber-600 mb-2">99%</div>
                <h4 className="text-sm font-semibold text-slate-800 mb-1">Data Accuracy</h4>
                <p className="text-xs text-slate-600">Form validation</p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg border border-slate-200">
                <div className="text-3xl font-bold text-emerald-600 mb-2">87%</div>
                <h4 className="text-sm font-semibold text-slate-800 mb-1">Time Saved</h4>
                <p className="text-xs text-slate-600">vs Traditional</p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg border border-slate-200">
                <div className="text-3xl font-bold text-violet-600 mb-2">92%</div>
                <h4 className="text-sm font-semibold text-slate-800 mb-1">Efficiency Gain</h4>
                <p className="text-xs text-slate-600">Process improvement</p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg border border-slate-200">
                <div className="text-3xl font-bold text-blue-600 mb-2">96%</div>
                <h4 className="text-sm font-semibold text-slate-800 mb-1">User Satisfaction</h4>
                <p className="text-xs text-slate-600">Professional rating</p>
              </div>
            </div>
          </div>

          {/* Licensed Broker Credibility */}
          <div className="bg-emerald-50 border-2 border-emerald-200 rounded-lg p-8">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-emerald-600 rounded-xl flex items-center justify-center">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-800 font-serif">Built by Licensed Real Estate Professionals</h3>
                </div>
                <p className="text-slate-700 text-lg mb-6">
                  Created by active real estate brokers who understand the complexities of purchase agreements.
                  Our platform combines decades of industry expertise with modern automation technology.
                </p>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Licensed Broker Founded</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Industry Expertise</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Legal Compliance</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Professional Support</span>
                  </div>
                </div>
              </div>

              <div className="flex-shrink-0">
                <div className="relative">
                  <div className="w-32 h-32 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-full flex items-center justify-center shadow-lg">
                    <div className="text-4xl">🏆</div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center shadow-md">
                    <span className="text-xs font-bold text-white">#1</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}