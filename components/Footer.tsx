import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-slate-900 text-slate-300 py-12 border-t border-[#006AFF]/20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 bg-[#006AFF] rounded-full"></div>
                <h3 className="text-2xl font-bold text-white">PropBolt</h3>
              </div>
              <p className="text-slate-400 mb-6 leading-relaxed max-w-md">
                Create professional Florida purchase agreements in minutes.
                State-compliant documentation with smart data integration built by licensed brokers.
              </p>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#006AFF]/10 border border-[#006AFF]/30 rounded-lg text-[#006AFF] text-sm font-semibold">
                <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
                <span>Built by Licensed Brokers</span>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h4 className="text-sm font-bold text-white uppercase mb-4 tracking-wide">Product</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="#features" className="text-slate-400 hover:text-[#006AFF] transition-colors">Features</Link></li>
                <li><Link href="#demo" className="text-slate-400 hover:text-[#006AFF] transition-colors">Live Demo</Link></li>
                <li><Link href="#pricing" className="text-slate-400 hover:text-[#006AFF] transition-colors">Pricing</Link></li>
              </ul>
            </div>

            {/* Legal Links */}
            <div>
              <h4 className="text-sm font-bold text-white uppercase mb-4 tracking-wide">Legal</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="/privacy" className="text-slate-400 hover:text-[#006AFF] transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-slate-400 hover:text-[#006AFF] transition-colors">Terms of Service</Link></li>
                <li><Link href="/disclaimer" className="text-slate-400 hover:text-[#006AFF] transition-colors">Disclaimer</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-slate-700">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-sm text-slate-500">
                © {new Date().getFullYear()} PropBolt. All rights reserved.
              </p>
              <p className="text-xs text-slate-500 text-center md:text-right max-w-md">
                PropBolt is a documentation service only. We do not provide legal advice.
                <br />Consult a licensed attorney before finalizing any legal documents.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}