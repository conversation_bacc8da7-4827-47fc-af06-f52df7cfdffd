export interface PropertyInfo {
  sellerName: string;
  buyerName: string;
  streetAddress: string;
  county: string;
  legalDescription1: string;
  legalDescription2: string;
  propertyTaxId: string;
  personalPropertyIncluded: string;
  additionalPersonalProperty: string;
  itemsExcluded: string;
  sellerPhone?: string;
  sellerEmail?: string;
  buyerPhone?: string;
  fixturesIncluded?: string;
  otherItems?: string;
  additionalPropertyDetails?: string;
  utilitiesElectric?: string;
  utilitiesGas?: string;
  utilitiesWater?: string;
  utilitiesCable?: string;
  utilitiesInternet?: string;
  utilitiesTrash?: string;
  utilitiesSecurity?: string;
  maintenancePool?: string;
  maintenanceLandscape?: string;
  maintenanceHvac?: string;
  maintenanceDock?: string;
  rentalRestrictions?: string;
  petRestrictions?: string;
  parkingSpaces?: string;
  storageAreas?: string;
  outdoorFeatures?: string;
  recentImprovements?: string;
  warrantyInformation?: string;
  disclosureItems?: string;
}

export interface FinancialInfo {
  purchasePrice: number;
  initialDeposit: number;
  balanceToClose: number;
  closingDate: string;
  additionalDeposit?: number;
  loanAmount?: number;
  downPayment?: number;
  closingCosts?: number;
  inspectionFee?: number;
  appraisalFee?: number;
  surveyCost?: number;
  specialAssessments?: number;
  hoaFees?: number;
  propertyTaxes?: number;
  insuranceCost?: number;
  utilityCosts?: number;
  maintenanceCosts?: number;
  inspectionDeadline?: string;
  financingDeadline?: string;
  appraisalDeadline?: string;
  possessionDate?: string;
  finalWalkthrough?: string;
  documentDelivery?: string;
}

export interface PaymentMethod {
  paymentType: 'cash' | 'financing';
  loanType: string;
  interestRateType: string;
  financingAmount: number; // Auto-calculated
}

export interface EscrowInfo {
  escrowAgentName: string;
  escrowAgentAddress: string;
  escrowAgentEmail?: string;
  escrowAgentFax?: string;
}

export interface PartyDetails {
  sellerInitials: string;
  buyerInitials: string;
  contractDate: string;
  communicationPreferences?: string;
  keyInformation?: string;
  alarmCodes?: string;
  utilityTransfers?: string;
  sellerAttorney?: string;
  buyerAttorney?: string;
  titleCompany?: string;
  realEstateAgentSeller?: string;
  realEstateAgentBuyer?: string;
  lenderName?: string;
  loanOfficer?: string;
  appraiser?: string;
  inspector?: string;
  surveyor?: string;
  insuranceAgent?: string;
  hoaManagement?: string;
}

export interface TitleClosingLogic {
  closingOption: 'seller-designates' | 'buyer-designates';
  sellerDesignatesClosingAgent: boolean;
  buyerDesignatesClosingAgent: boolean;
}

export interface Inspections {
  [key: string]: boolean;
}

export interface Appliances {
  [key: string]: boolean;
}

export interface ClosingCheckboxes {
  [key: string]: boolean;
}

export interface Contingencies {
  inspectionPeriod: string; // Number of days for inspection period
  financingDeadline: string; // Number of days for financing deadline
  appraisalContingency: string; // Number of days for appraisal contingency
}

export interface AutoGeneratedFields {
  countyTaxId: string; // Auto-generates based on county
  multipleBuyerInitials: string[]; // Populates all buyer initial fields
  multipleSellerInitials: string[]; // Populates all seller initial fields
  multipleDateFields: string[]; // Populates all date fields
  additionalTerms: string[]; // Split into separate PDF fields
}

export interface FormData {
  propertyInfo: PropertyInfo;
  financialInfo: FinancialInfo;
  paymentMethod: PaymentMethod;
  escrowInfo: EscrowInfo;
  partyDetails: PartyDetails;
  titleClosingLogic: TitleClosingLogic;
  contingencies?: Contingencies; // Optional contingency periods
  autoGeneratedFields: AutoGeneratedFields;
  additionalTerms: string[];
  inspections: Inspections;
  appliances: Appliances;
  closing: ClosingCheckboxes;
}

export const initialFormData: FormData = {
  propertyInfo: {
    sellerName: '',
    buyerName: '',
    streetAddress: '',
    county: '',
    legalDescription1: '',
    legalDescription2: '',
    propertyTaxId: '',
    personalPropertyIncluded: '',
    additionalPersonalProperty: '',
    itemsExcluded: '',
    sellerPhone: '',
    sellerEmail: '',
    buyerPhone: '',
    fixturesIncluded: '',
    otherItems: '',
    additionalPropertyDetails: '',
    utilitiesElectric: '',
    utilitiesGas: '',
    utilitiesWater: '',
    utilitiesCable: '',
    utilitiesInternet: '',
    utilitiesTrash: '',
    utilitiesSecurity: '',
    maintenancePool: '',
    maintenanceLandscape: '',
    maintenanceHvac: '',
    maintenanceDock: '',
    rentalRestrictions: '',
    petRestrictions: '',
    parkingSpaces: '',
    storageAreas: '',
    outdoorFeatures: '',
    recentImprovements: '',
    warrantyInformation: '',
    disclosureItems: '',
  },
  financialInfo: {
    purchasePrice: 0,
    initialDeposit: 0,
    balanceToClose: 0,
    closingDate: '',
    additionalDeposit: 0,
    loanAmount: 0,
    downPayment: 0,
    closingCosts: 0,
    inspectionFee: 0,
    appraisalFee: 0,
    surveyCost: 0,
    specialAssessments: 0,
    hoaFees: 0,
    propertyTaxes: 0,
    insuranceCost: 0,
    utilityCosts: 0,
    maintenanceCosts: 0,
    inspectionDeadline: '',
    financingDeadline: '',
    appraisalDeadline: '',
    possessionDate: '',
    finalWalkthrough: '',
    documentDelivery: '',
  },
  paymentMethod: {
    paymentType: 'cash',
    loanType: '',
    interestRateType: '',
    financingAmount: 0,
  },
  escrowInfo: {
    escrowAgentName: '',
    escrowAgentAddress: '',
    escrowAgentEmail: '',
    escrowAgentFax: '',
  },
  partyDetails: {
    sellerInitials: '',
    buyerInitials: '',
    contractDate: '',
    communicationPreferences: '',
    keyInformation: '',
    alarmCodes: '',
    utilityTransfers: '',
    sellerAttorney: '',
    buyerAttorney: '',
    titleCompany: '',
    realEstateAgentSeller: '',
    realEstateAgentBuyer: '',
    lenderName: '',
    loanOfficer: '',
    appraiser: '',
    inspector: '',
    surveyor: '',
    insuranceAgent: '',
    hoaManagement: '',
  },
  titleClosingLogic: {
    closingOption: 'seller-designates',
    sellerDesignatesClosingAgent: false,
    buyerDesignatesClosingAgent: false,
  },
  contingencies: {
    inspectionPeriod: '',
    financingDeadline: '',
    appraisalContingency: '',
  },
  autoGeneratedFields: {
    countyTaxId: '',
    multipleBuyerInitials: [],
    multipleSellerInitials: [],
    multipleDateFields: [],
    additionalTerms: [],
  },
  additionalTerms: [''],
  inspections: {},
  appliances: {},
  closing: {},
};