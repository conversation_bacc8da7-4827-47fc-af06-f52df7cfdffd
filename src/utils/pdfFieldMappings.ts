// Simple validation utilities for form fields
// This replaces the complex PDF mapping system with basic validation

export interface ValidationResult {
  sanitizedValue: string;
  isValid: boolean;
  error?: string;
}

export const validateTaxId = (value: string): ValidationResult => {
  // Basic tax ID validation - remove extra spaces and validate format
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Florida tax ID format: XX-XXXX-XXX-XX
  const taxIdPattern = /^\d{2}-\d{4}-\d{3}-\d{2}$/;
  const isValid = taxIdPattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Tax ID should be in format: XX-XXXX-XXX-XX' : undefined
  };
};

export const validateAddress = (value: string): ValidationResult => {
  // Basic address validation - clean up spacing and basic format check
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Basic address validation - should have some numbers and letters
  const hasNumbers = /\d/.test(sanitized);
  const hasLetters = /[a-zA-Z]/.test(sanitized);
  const isValid = (hasNumbers && hasLetters) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid address' : undefined
  };
};

export const validateName = (value: string): ValidationResult => {
  // Basic name validation - clean up spacing and check for valid characters
  const sanitized = value.trim().replace(/\s+/g, ' ');
  
  // Name should only contain letters, spaces, hyphens, and apostrophes
  const namePattern = /^[a-zA-Z\s\-']+$/;
  const isValid = namePattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Name should only contain letters, spaces, hyphens, and apostrophes' : undefined
  };
};

export const validateEmail = (value: string): ValidationResult => {
  const sanitized = value.trim().toLowerCase();
  
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailPattern.test(sanitized) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid email address' : undefined
  };
};

export const validatePhone = (value: string): ValidationResult => {
  // Clean up phone number - remove all non-digits
  const digitsOnly = value.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX if 10 digits
  let sanitized = value.trim();
  if (digitsOnly.length === 10) {
    sanitized = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`;
  }
  
  const isValid = digitsOnly.length === 10 || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid 10-digit phone number' : undefined
  };
};

export const validateCurrency = (value: string): ValidationResult => {
  // Remove currency symbols and clean up
  const cleaned = value.replace(/[$,]/g, '').trim();
  const sanitized = cleaned;
  
  const isValid = /^\d*\.?\d*$/.test(cleaned) || cleaned === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid amount' : undefined
  };
};

export const validateDate = (value: string): ValidationResult => {
  const sanitized = value.trim();
  
  // Basic date validation - check if it's a valid date format
  const isValid = !isNaN(Date.parse(sanitized)) || sanitized === '';
  
  return {
    sanitizedValue: sanitized,
    isValid,
    error: !isValid && sanitized !== '' ? 'Please enter a valid date' : undefined
  };
};

// Format data for your new API
export const formatDataForNewAPI = (formData: any) => {
  // This function converts your form data to the format expected by your deployed API
  // Based on your API_DOCUMENTATION.md structure

  // Map financing checkboxes
  const financing = {
    cash: formData.paymentMethod?.paymentType === 'cash',
    conventional: formData.paymentMethod?.loanType === 'conventional',
    fha: formData.paymentMethod?.loanType === 'fha',
    va: formData.paymentMethod?.loanType === 'va',
    usda: formData.paymentMethod?.loanType === 'usda',
    other: formData.paymentMethod?.loanType === 'other',
  };

  // Map inspections and appliances (already in correct structure)
  const inspections = formData.inspections || {};
  const appliances = formData.appliances || {};
  const closing = formData.closing || {};

  // Map agent info (example structure, adjust as needed)
  const seller_agent = {
    name: formData.partyDetails?.realEstateAgentSeller || '',
    company: formData.partyDetails?.sellerAgentCompany || '',
    license: formData.partyDetails?.sellerAgentLicense || '',
    phone: formData.propertyInfo?.sellerPhone || '',
    email: formData.propertyInfo?.sellerEmail || '',
  };
  const buyer_agent = {
    name: formData.partyDetails?.realEstateAgentBuyer || '',
    company: formData.partyDetails?.buyerAgentCompany || '',
    license: formData.partyDetails?.buyerAgentLicense || '',
    phone: formData.propertyInfo?.buyerPhone || '',
    email: formData.partyDetails?.buyerEmail || '',
  };

  // Utilities and maintenance (grouped as example, adjust as needed)
  const utilities = {
    electric: formData.propertyInfo?.utilitiesElectric || '',
    gas: formData.propertyInfo?.utilitiesGas || '',
    water: formData.propertyInfo?.utilitiesWater || '',
    cable: formData.propertyInfo?.utilitiesCable || '',
    internet: formData.propertyInfo?.utilitiesInternet || '',
    trash: formData.propertyInfo?.utilitiesTrash || '',
    security: formData.propertyInfo?.utilitiesSecurity || '',
  };
  const maintenance = {
    pool: formData.propertyInfo?.maintenancePool || '',
    landscape: formData.propertyInfo?.maintenanceLandscape || '',
    hvac: formData.propertyInfo?.maintenanceHvac || '',
    dock: formData.propertyInfo?.maintenanceDock || '',
  };

  // Compose the payload
  return {
    // Basic Information
    seller_name: formData.propertyInfo?.sellerName || '',
    buyer_name: formData.propertyInfo?.buyerName || '',
    purchase_price: formData.financialInfo?.purchasePrice || 0,
    property_address: formData.propertyInfo?.streetAddress || '',
    county: formData.propertyInfo?.county || '',
    tax_id: formData.propertyInfo?.propertyTaxId || '',
    legal_description: formData.propertyInfo?.legalDescription1 || '',
    closing_date: formData.financialInfo?.closingDate || '',
    contract_date: formData.partyDetails?.contractDate || '',

    // Contact Details
    seller_phone: formData.propertyInfo?.sellerPhone || '',
    seller_email: formData.propertyInfo?.sellerEmail || '',
    buyer_phone: formData.propertyInfo?.buyerPhone || '',

    // Financial Terms
    initial_deposit: formData.financialInfo?.initialDeposit || 0,
    additional_deposit: formData.financialInfo?.additionalDeposit || 0,
    loan_amount: formData.financialInfo?.loanAmount || 0,
    down_payment: formData.financialInfo?.downPayment || 0,
    closing_costs: formData.financialInfo?.closingCosts || 0,
    balance_to_close: formData.financialInfo?.balanceToClose || 0,
    inspection_fee: formData.financialInfo?.inspectionFee || 0,
    appraisal_fee: formData.financialInfo?.appraisalFee || 0,
    survey_cost: formData.financialInfo?.surveyCost || 0,
    special_assessments: formData.financialInfo?.specialAssessments || 0,
    hoa_fees: formData.financialInfo?.hoaFees || 0,
    property_taxes: formData.financialInfo?.propertyTaxes || 0,
    insurance_cost: formData.financialInfo?.insuranceCost || 0,
    utility_costs: formData.financialInfo?.utilityCosts || 0,
    maintenance_costs: formData.financialInfo?.maintenanceCosts || 0,

    // Dates & Deadlines
    inspection_deadline: formData.financialInfo?.inspectionDeadline || '',
    financing_deadline: formData.financialInfo?.financingDeadline || '',
    appraisal_deadline: formData.financialInfo?.appraisalDeadline || '',
    possession_date: formData.financialInfo?.possessionDate || '',
    final_walkthrough: formData.financialInfo?.finalWalkthrough || '',
    document_delivery: formData.financialInfo?.documentDelivery || '',

    // Professional Services
    seller_attorney: formData.partyDetails?.sellerAttorney || '',
    buyer_attorney: formData.partyDetails?.buyerAttorney || '',
    title_company: formData.partyDetails?.titleCompany || '',
    real_estate_agent_seller: formData.partyDetails?.realEstateAgentSeller || '',
    real_estate_agent_buyer: formData.partyDetails?.realEstateAgentBuyer || '',
    lender_name: formData.partyDetails?.lenderName || '',
    loan_officer: formData.partyDetails?.loanOfficer || '',
    appraiser: formData.partyDetails?.appraiser || '',
    inspector: formData.partyDetails?.inspector || '',
    surveyor: formData.partyDetails?.surveyor || '',
    insurance_agent: formData.partyDetails?.insuranceAgent || '',
    hoa_management: formData.partyDetails?.hoaManagement || '',

    // Property Details
    personal_property_included: formData.propertyInfo?.personalPropertyIncluded || '',
    additional_personal_property_list: formData.propertyInfo?.additionalPersonalProperty || '',
    items_excluded_from_purchase: formData.propertyInfo?.itemsExcluded || '',
    fixtures_included: formData.propertyInfo?.fixturesIncluded || '',
    other_items: formData.propertyInfo?.otherItems || '',
    additional_property_details: formData.propertyInfo?.additionalPropertyDetails || '',
    rental_restrictions: formData.propertyInfo?.rentalRestrictions || '',
    pet_restrictions: formData.propertyInfo?.petRestrictions || '',
    parking_spaces: formData.propertyInfo?.parkingSpaces || '',
    storage_areas: formData.propertyInfo?.storageAreas || '',
    outdoor_features: formData.propertyInfo?.outdoorFeatures || '',
    recent_improvements: formData.propertyInfo?.recentImprovements || '',
    warranty_information: formData.propertyInfo?.warrantyInformation || '',
    disclosure_items: formData.propertyInfo?.disclosureItems || '',

    // Utilities & Maintenance
    utilities,
    maintenance,

    // Escrow/Title Company
    escrow_agent_name: formData.escrowInfo?.escrowAgentName || '',
    escrow_agent_address: formData.escrowInfo?.escrowAgentAddress || '',
    escrow_agent_email: formData.escrowInfo?.escrowAgentEmail || '',
    escrow_agent_fax: formData.escrowInfo?.escrowAgentFax || '',

    // Communication & Misc
    communication_preferences: formData.partyDetails?.communicationPreferences || '',
    key_information: formData.partyDetails?.keyInformation || '',
    alarm_codes: formData.partyDetails?.alarmCodes || '',
    utility_transfers: formData.partyDetails?.utilityTransfers || '',

    // Additional Terms
    additional_terms: (formData.additionalTerms || []).filter(Boolean).join('\n'),

    // Contingencies & Special Conditions
    contingencies: formData.contingencies?.inspectionPeriod || '',
    special_conditions: formData.contingencies?.appraisalContingency || '',

    // Checkbox Groups
    financing,
    inspections,
    appliances,
    closing,

    // Agents (nested, optional)
    seller_agent,
    buyer_agent,
  };
};
