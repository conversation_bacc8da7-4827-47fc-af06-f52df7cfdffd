import React, { useState, useEffect } from 'react';
import { MapPin, Building, FileText, CheckCircle, Clock, Shield, Zap, TrendingUp, DollarSign, Users, Home, Search, ArrowRight, Play, Pause } from 'lucide-react';

interface AnimatedSectionsProps {
  onAddressInputFocus: () => void;
}

export function AnimatedSections({ onAddressInputFocus }: AnimatedSectionsProps) {
  const [activeSection, setActiveSection] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [countdown, setCountdown] = useState(5);
  const [propertyData, setPropertyData] = useState({
    address: '',
    owner: '',
    value: '',
    legalDescription: '',
    taxId: '',
    county: ''
  });

  const sections = [
    {
      id: 'live-intelligence',
      title: 'Live Property Intelligence',
      subtitle: 'Watch real property data populate instantly'
    },
    {
      id: 'smart-forms',
      title: 'Smart Form Pre-Population',
      subtitle: 'See how your actual forms get filled automatically'
    },
    {
      id: 'florida-requirements',
      title: 'Florida Requirements Generator',
      subtitle: 'Dynamic form requirements based on your property'
    }
  ];

  // Auto-rotation logic
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setActiveSection(current => (current + 1) % sections.length);
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isPlaying, sections.length]);

  // Typing animation function
  const animateTyping = (field: string, text: string, delay: number) => {
    setTimeout(() => {
      let currentIndex = 0;
      const typingInterval = setInterval(() => {
        if (currentIndex <= text.length) {
          setPropertyData(prev => ({
            ...prev,
            [field]: text.substring(0, currentIndex)
          }));
          currentIndex++;
        } else {
          clearInterval(typingInterval);
        }
      }, 50);
    }, delay);
  };

  // Demo data for different sections
  useEffect(() => {
    const demoData = {
      address: '123 Ocean Drive, Miami Beach, FL 33139',
      owner: 'Sarah & Michael Rodriguez',
      value: '$850,000',
      legalDescription: 'Lot 15, Block A, Ocean View Estates',
      taxId: '02-3141-001-0150',
      county: 'Miami-Dade'
    };

    if (activeSection === 0) {
      // Reset and animate
      setPropertyData({
        address: '',
        owner: '',
        value: '',
        legalDescription: '',
        taxId: '',
        county: ''
      });

      animateTyping('address', demoData.address, 500);
      animateTyping('owner', demoData.owner, 1500);
      animateTyping('value', demoData.value, 2500);
      animateTyping('legalDescription', demoData.legalDescription, 3500);
      animateTyping('taxId', demoData.taxId, 4500);
      animateTyping('county', demoData.county, 5500);
    }
  }, [activeSection]);

  const handleSectionClick = (index: number) => {
    setActiveSection(index);
    setCountdown(5);
    setIsPlaying(false);
    setTimeout(() => setIsPlaying(true), 1000);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Section Navigation */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        {sections.map((section, index) => (
          <button
            key={section.id}
            onClick={() => handleSectionClick(index)}
            className={`flex-1 p-6 rounded-xl border-2 transition-all duration-300 text-left ${
              activeSection === index
                ? 'border-[#006AFF] bg-[#006AFF]/5 shadow-lg'
                : 'border-slate-200 hover:border-slate-300 bg-white hover:bg-slate-50'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className={`font-bold text-lg ${
                activeSection === index ? 'text-[#006AFF]' : 'text-slate-900'
              }`}>
                {section.title}
              </h3>
              {activeSection === index && isPlaying && (
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full border-2 border-[#006AFF] flex items-center justify-center">
                    <span className="text-[#006AFF] text-sm font-bold">{countdown}</span>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsPlaying(!isPlaying);
                    }}
                    className="p-1 hover:bg-[#006AFF]/10 rounded"
                  >
                    {isPlaying ? <Pause className="h-4 w-4 text-[#006AFF]" /> : <Play className="h-4 w-4 text-[#006AFF]" />}
                  </button>
                </div>
              )}
            </div>
            <p className="text-slate-600 text-sm">{section.subtitle}</p>
          </button>
        ))}
      </div>

      {/* Animated Content */}
      <div className="min-h-[500px] relative">
        {/* Section 1: Live Property Intelligence */}
        {activeSection === 0 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Side - Address Input Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Smart Address Search</h3>
                <div className="relative">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={propertyData.address}
                    className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#006AFF] focus:border-[#006AFF] transition-all"
                    placeholder="Start typing address..."
                    readOnly
                  />
                </div>
                
                {propertyData.address && (
                  <div className="bg-blue-50 rounded-xl p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-2 text-[#006AFF] text-sm font-medium mb-2">
                      <MapPin className="h-4 w-4" />
                      <span>Property Found!</span>
                    </div>
                    <p className="text-sm text-gray-600">FormFlorida API retrieving details...</p>
                  </div>
                )}
              </div>

              {/* Right Side - Property Data Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Retrieved Property Data</h3>
                
                <div className="space-y-3">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <label className="text-sm font-medium text-gray-600">Current Owner</label>
                    <div className="text-gray-900 font-medium">{propertyData.owner || '...'}</div>
                  </div>
                  
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <label className="text-sm font-medium text-gray-600">Estimated Value</label>
                    <div className="text-green-600 font-bold text-lg">{propertyData.value || '...'}</div>
                  </div>
                  
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <label className="text-sm font-medium text-gray-600">Legal Description</label>
                    <div className="text-gray-900 font-medium">{propertyData.legalDescription || '...'}</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <label className="text-sm font-medium text-gray-600">Tax ID</label>
                      <div className="text-gray-900 font-medium text-sm">{propertyData.taxId || '...'}</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <label className="text-sm font-medium text-gray-600">County</label>
                      <div className="text-gray-900 font-medium">{propertyData.county || '...'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Section 2: Smart Form Pre-Population */}
        {activeSection === 1 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">📝 Smart Form Pre-Population</h3>
              <p className="text-gray-600">Watch how property data automatically fills your purchase agreement</p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Florida Purchase Agreement Form</h4>
                <div className="space-y-3 p-6 bg-gray-50 rounded-lg">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Property Address:</span>
                    <span className="text-sm font-medium text-[#006AFF]">Auto-filled ✓</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Legal Description:</span>
                    <span className="text-sm font-medium text-[#006AFF]">Auto-filled ✓</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tax ID Number:</span>
                    <span className="text-sm font-medium text-[#006AFF]">Auto-filled ✓</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">County Information:</span>
                    <span className="text-sm font-medium text-[#006AFF]">Auto-filled ✓</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Time Saved</h4>
                <div className="p-6 bg-green-50 rounded-lg border-2 border-green-200">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">15 minutes</div>
                    <p className="text-sm text-green-700">Average time saved per agreement</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Section 3: Florida Requirements Generator */}
        {activeSection === 2 && (
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4 bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">⚖️ Florida Requirements Generator</h3>
              <p className="text-gray-600">Dynamic compliance based on your specific property</p>
            </div>
            
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Property Analysis</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <Building className="h-5 w-5 text-blue-600" />
                      <span className="text-sm">Built in 1985 - Lead paint disclosure not required</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                      <Home className="h-5 w-5 text-yellow-600" />
                      <span className="text-sm">HOA detected - Additional disclosures recommended</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                      <Shield className="h-5 w-5 text-green-600" />
                      <span className="text-sm">Flood zone analysis complete</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Required Documents</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-4 bg-green-50 border-2 border-green-200 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div className="flex-1">
                        <h5 className="font-semibold text-green-800">Florida Purchase Agreement</h5>
                        <p className="text-sm text-green-600">Required for all transactions</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-4 bg-yellow-50 border-2 border-yellow-200 rounded-lg">
                      <Home className="h-5 w-5 text-yellow-600" />
                      <div className="flex-1">
                        <h5 className="font-semibold text-yellow-800">HOA Disclosure</h5>
                        <p className="text-sm text-yellow-600">Recommended for this property</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
