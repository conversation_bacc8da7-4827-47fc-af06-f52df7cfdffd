import React from 'react';
import { Clock, DollarSign, FileText, Shield, CheckCircle, AlertTriangle } from 'lucide-react';

export function AnimatedJourney() {
  return (
    <section className="py-20 bg-white border-t border-[#006AFF]/20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-[#006AFF]/10 border border-[#006AFF]/30 rounded-lg text-[#006AFF] text-sm font-semibold mb-8">
              <div className="w-2 h-2 bg-[#006AFF] rounded-full animate-pulse"></div>
              <span>PROFESSIONAL DOCUMENT AUTOMATION</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">
              Why Choose Professional Document Automation?
            </h2>
            <p className="text-xl text-slate-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              See the difference between manual document creation and our intelligent automation platform
            </p>
          </div>

          {/* Comparison Grid */}
          <div className="grid lg:grid-cols-2 gap-12 mb-16">
            {/* Manual Process */}
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-red-100 border border-red-200 rounded-lg text-red-700 text-sm font-semibold mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <span>MANUAL PROCESS</span>
                </div>
                <h3 className="text-2xl font-bold text-slate-800 mb-2">Traditional Method</h3>
                <p className="text-slate-600">Time-consuming and error-prone</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 mb-1">Research Property Details</h4>
                    <p className="text-sm text-red-600">Manually lookup property records, tax information, and legal descriptions</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-red-500" />
                      <span className="text-xs text-red-600 font-medium">30-45 minutes</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 mb-1">Fill Forms by Hand</h4>
                    <p className="text-sm text-red-600">Type each field individually, prone to typos and formatting errors</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-red-500" />
                      <span className="text-xs text-red-600 font-medium">45-60 minutes</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 mb-1">Review & Corrections</h4>
                    <p className="text-sm text-red-600">Multiple rounds of proofreading and error correction</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-red-500" />
                      <span className="text-xs text-red-600 font-medium">15-30 minutes</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-red-100 border-2 border-red-300 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-700 mb-1">90-135 minutes</div>
                    <p className="text-sm text-red-600">Total time per agreement</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Automated Process */}
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#006AFF]/10 border border-[#006AFF]/20 rounded-lg text-[#006AFF] text-sm font-semibold mb-4">
                  <CheckCircle className="h-4 w-4" />
                  <span>FORMFLORIDA AUTOMATION</span>
                </div>
                <h3 className="text-2xl font-bold text-slate-800 mb-2">Our Smart Platform</h3>
                <p className="text-slate-600">Intelligent and lightning-fast</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-4 p-4 bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-lg">
                  <div className="w-8 h-8 bg-[#006AFF] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#006AFF] mb-1">Instant Property Intelligence</h4>
                    <p className="text-sm text-slate-600">AI-powered property lookup with real-time data integration</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-[#006AFF]" />
                      <span className="text-xs text-[#006AFF] font-medium">30 seconds</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-lg">
                  <div className="w-8 h-8 bg-[#006AFF] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#006AFF] mb-1">Smart Form Population</h4>
                    <p className="text-sm text-slate-600">Automatic field completion with perfect formatting and compliance</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-[#006AFF]" />
                      <span className="text-xs text-[#006AFF] font-medium">2 minutes</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-[#006AFF]/5 border border-[#006AFF]/20 rounded-lg">
                  <div className="w-8 h-8 bg-[#006AFF] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#006AFF] mb-1">Instant Generation</h4>
                    <p className="text-sm text-slate-600">Professional PDF ready for signatures with built-in compliance</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-4 w-4 text-[#006AFF]" />
                      <span className="text-xs text-[#006AFF] font-medium">30 seconds</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-100 border-2 border-green-300 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700 mb-1">3 minutes</div>
                    <p className="text-sm text-green-600">Total time per agreement</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="p-6 bg-green-50 border border-green-200 rounded-xl">
              <div className="text-3xl font-bold text-green-600 mb-2">97%</div>
              <p className="text-green-700 font-medium">Time Saved</p>
              <p className="text-sm text-green-600 mt-1">vs. manual process</p>
            </div>
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-xl">
              <div className="text-3xl font-bold text-blue-600 mb-2">100%</div>
              <p className="text-blue-700 font-medium">Accuracy Rate</p>
              <p className="text-sm text-blue-600 mt-1">No more typos or errors</p>
            </div>
            <div className="p-6 bg-purple-50 border border-purple-200 rounded-xl">
              <div className="text-3xl font-bold text-purple-600 mb-2">$2,400</div>
              <p className="text-purple-700 font-medium">Monthly Savings</p>
              <p className="text-sm text-purple-600 mt-1">For average agent</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
