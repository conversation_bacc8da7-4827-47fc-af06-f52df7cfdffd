import React from 'react';
import { Users, Calendar, User } from 'lucide-react';
import { PartyDetails as PartyDetailsType } from '../types/FormTypes';

interface PartyDetailsProps {
  data: PartyDetailsType;
  onChange: (data: PartyDetailsType) => void;
}

const PartyDetails: React.FC<PartyDetailsProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof PartyDetailsType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-4">
          <Users className="h-6 w-6 text-orange-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Contract Details</h3>
        <p className="text-gray-600">Enter the contract date and party initials</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Calendar className="h-4 w-4 text-blue-600" />
            Contract Date
          </label>
          <input
            type="date"
            value={data.contractDate}
            onChange={(e) => handleChange('contractDate', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <User className="h-4 w-4 text-green-600" />
            Buyer Initials
          </label>
          <input
            type="text"
            value={data.buyerInitials}
            onChange={(e) => handleChange('buyerInitials', e.target.value.toUpperCase())}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg font-semibold text-center"
            maxLength={4}
            placeholder="AB"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <User className="h-4 w-4 text-purple-600" />
            Seller Initials
          </label>
          <input
            type="text"
            value={data.sellerInitials}
            onChange={(e) => handleChange('sellerInitials', e.target.value.toUpperCase())}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg font-semibold text-center"
            maxLength={4}
            placeholder="JS"
            required
          />
        </div>
      </div>

      {/* Additional Party Details */}
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Communication Preferences</label>
        <input
          type="text"
          value={data.communicationPreferences || ''}
          onChange={(e) => handleChange('communicationPreferences', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Preferred method of communication (email, phone, etc.)"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Key Information</label>
        <input
          type="text"
          value={data.keyInformation || ''}
          onChange={(e) => handleChange('keyInformation', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Key pickup/dropoff details"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Alarm Codes</label>
        <input
          type="text"
          value={data.alarmCodes || ''}
          onChange={(e) => handleChange('alarmCodes', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Alarm/security codes for property"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Utility Transfers</label>
        <input
          type="text"
          value={data.utilityTransfers || ''}
          onChange={(e) => handleChange('utilityTransfers', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Instructions for utility transfers"
        />
      </div>

      <div className="bg-orange-50 rounded-xl p-6 border border-orange-200">
        <h4 className="font-semibold text-orange-900 mb-2">Initials Usage</h4>
        <p className="text-sm text-orange-800">
          These initials will be automatically populated throughout the purchase agreement document 
          wherever buyer and seller acknowledgments are required.
        </p>
      </div>

      {/* Legal/Professional Contacts Section */}
      <div className="space-y-6 mt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Professional Contacts</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Seller Attorney</label>
            <input
              type="text"
              value={data.sellerAttorney || ''}
              onChange={(e) => handleChange('sellerAttorney', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Seller's attorney name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Buyer Attorney</label>
            <input
              type="text"
              value={data.buyerAttorney || ''}
              onChange={(e) => handleChange('buyerAttorney', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Buyer's attorney name"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Title Company</label>
            <input
              type="text"
              value={data.titleCompany || ''}
              onChange={(e) => handleChange('titleCompany', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Title company name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Seller's Agent</label>
            <input
              type="text"
              value={data.realEstateAgentSeller || ''}
              onChange={(e) => handleChange('realEstateAgentSeller', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Seller's agent name"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Buyer's Agent</label>
            <input
              type="text"
              value={data.realEstateAgentBuyer || ''}
              onChange={(e) => handleChange('realEstateAgentBuyer', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Buyer's agent name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Lender Name</label>
            <input
              type="text"
              value={data.lenderName || ''}
              onChange={(e) => handleChange('lenderName', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Lender name"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Loan Officer</label>
            <input
              type="text"
              value={data.loanOfficer || ''}
              onChange={(e) => handleChange('loanOfficer', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Loan officer name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Appraiser</label>
            <input
              type="text"
              value={data.appraiser || ''}
              onChange={(e) => handleChange('appraiser', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Appraiser name"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Inspector</label>
            <input
              type="text"
              value={data.inspector || ''}
              onChange={(e) => handleChange('inspector', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Inspector name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Surveyor</label>
            <input
              type="text"
              value={data.surveyor || ''}
              onChange={(e) => handleChange('surveyor', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Surveyor name"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Insurance Agent</label>
            <input
              type="text"
              value={data.insuranceAgent || ''}
              onChange={(e) => handleChange('insuranceAgent', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Insurance agent name"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">HOA Management</label>
            <input
              type="text"
              value={data.hoaManagement || ''}
              onChange={(e) => handleChange('hoaManagement', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="HOA management company"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartyDetails;
