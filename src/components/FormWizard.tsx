import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { FileText, Download, Check, AlertCircle, ArrowRight, MapPin, Search, Loader2, Eye } from 'lucide-react';
import PropertyInfo from './PropertyInfo';
import PropertyDetails from './PropertyDetails';
import FinancialBasic from './FinancialBasic';
import FinancialDates from './FinancialDates';
import PaymentMethod from './PaymentMethod';
import EscrowInfo from './EscrowInfo';
import PartyDetails from './PartyDetails';
import TitleClosingLogic from './TitleClosingLogic';
import AutoGeneratedFields from './AutoGeneratedFields';
import AdditionalTerms from './AdditionalTerms';
import PDFMappingDebug from './PDFMappingDebug';
import { FormData, initialFormData } from '../types/FormTypes';
import { updateFinancialCalculations, validateForm, formatFormDataForPDF, formatDataForNewAPI } from '../utils/calculations';
import { fetchAutoComplete, fetchPropertyDetail, debounce, formatFullAddress } from '../utils/propertyApi';

const FormWizard: React.FC = () => {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState(-1); // Start at -1 for landing page
  const [addressInput, setAddressInput] = useState('');
  const [addressSuggestions, setAddressSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoadingAutocomplete, setIsLoadingAutocomplete] = useState(false);
  const [isLoadingPropertyDetail, setIsLoadingPropertyDetail] = useState(false);
  const [apiError, setApiError] = useState<string>('');
  const inputRef = useRef(null);
  const [dropdownStyle, setDropdownStyle] = useState({ top: 0, left: 0, width: 0 });

  const steps = [
    { id: 'property-basic', title: 'Property & Parties', component: PropertyInfo },
    { id: 'property-details', title: 'Property Details', component: PropertyDetails },
    { id: 'financial-basic', title: 'Purchase Price', component: FinancialBasic },
    { id: 'financial-dates', title: 'Dates & Timeline', component: FinancialDates },
    { id: 'payment', title: 'Payment Method', component: PaymentMethod },
    { id: 'escrow', title: 'Escrow & Title', component: EscrowInfo },
    { id: 'parties', title: 'Contract Details', component: PartyDetails },
    { id: 'closing', title: 'Closing Agent', component: TitleClosingLogic },
    { id: 'terms', title: 'Additional Terms', component: AdditionalTerms },
    { id: 'preview', title: 'Review & Generate', component: null }, // Special final step
  ];

  // Debounced autocomplete function
  const debouncedAutocomplete = debounce(async (searchValue: string) => {
    if (searchValue.length < 3) {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      setIsLoadingAutocomplete(false);
      return;
    }

    setIsLoadingAutocomplete(true);
    setApiError('');

    try {
      const suggestions = await fetchAutoComplete(searchValue);
      setAddressSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } catch (error) {
      console.error('Autocomplete error:', error);
      setApiError('Failed to load address suggestions');
      setAddressSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setIsLoadingAutocomplete(false);
    }
  }, 300);

  // Handle address input changes
  useEffect(() => {
    if (addressInput.trim()) {
      debouncedAutocomplete(addressInput);
    } else {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      setIsLoadingAutocomplete(false);
    }
  }, [addressInput]);

  // Update calculations when relevant fields change
  useEffect(() => {
    const updatedFormData = updateFinancialCalculations(formData);
    if (updatedFormData.paymentMethod.financingAmount !== formData.paymentMethod.financingAmount) {
      setFormData(updatedFormData);
    }
  }, [
    formData.financialInfo.purchasePrice,
    formData.financialInfo.initialDeposit,
    formData.paymentMethod.paymentType,
  ]);

  useEffect(() => {
    if (showSuggestions && inputRef.current) {
      const rect = (inputRef.current as HTMLElement).getBoundingClientRect();
      setDropdownStyle({
        top: rect.bottom + window.scrollY + 8, // 8px gap
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  }, [showSuggestions, addressSuggestions]);

  const handleAddressSelect = async (address: string) => {
    setAddressInput(address);
    setShowSuggestions(false);
    setIsLoadingPropertyDetail(true);
    setApiError('');

    try {
      const propertyDetail = await fetchPropertyDetail(address);
      
      if (propertyDetail) {
        const fullAddress = formatFullAddress(propertyDetail);
        
        setFormData({
          ...formData,
          propertyInfo: {
            ...formData.propertyInfo,
            streetAddress: fullAddress,
            county: propertyDetail.county || '',
            propertyTaxId: propertyDetail.parcel_number || propertyDetail.apn || '',
            legalDescription1: propertyDetail.legal_description || '',
          }
        });
      } else {
        // Fallback if no property detail found
        setFormData({
          ...formData,
          propertyInfo: {
            ...formData.propertyInfo,
            streetAddress: address
          }
        });
      }
      
      setCurrentStep(0); // Move to first step of the form
    } catch (error) {
      console.error('Property detail error:', error);
      setApiError('Failed to load property details');
      
      // Still allow user to proceed with just the address
      setFormData({
        ...formData,
        propertyInfo: {
          ...formData.propertyInfo,
          streetAddress: address
        }
      });
      setCurrentStep(0);
    } finally {
      setIsLoadingPropertyDetail(false);
    }
  };

  const handleStartForm = async () => {
    if (addressInput.trim()) {
      await handleAddressSelect(addressInput);
    }
  };

  const mockFormData: FormData = {
    propertyInfo: {
      sellerName: 'John Doe',
      buyerName: 'Jane Smith',
      streetAddress: '123 Main St',
      county: 'Miami-Dade',
      legalDescription1: 'LOT 1, BLOCK 1, MAIN STREET SUBDIVISION',
      legalDescription2: 'PLAT BOOK 25, PAGE 45, MIAMI-DADE COUNTY RECORDS',
      propertyTaxId: '02-3204-001-0010',
      personalPropertyIncluded: 'Refrigerator, Oven, Washer, Dryer',
      additionalPersonalProperty: 'Patio Furniture',
      itemsExcluded: 'Seller\'s personal artwork',
    },
    financialInfo: {
      purchasePrice: 500000,
      initialDeposit: 25000,
      balanceToClose: 170000,
      closingDate: '2024-07-01',
    },
    paymentMethod: {
      paymentType: 'financing',
      loanType: 'conventional',
      interestRateType: 'fixed',
      financingAmount: 300000,
    },
    escrowInfo: {
      escrowAgentName: 'ABC Title Company',
      escrowAgentAddress: '789 Title Blvd, Miami, FL 33101',
    },
    partyDetails: {
      sellerInitials: 'JD',
      buyerInitials: 'JS',
      contractDate: '2024-06-01',
    },
    titleClosingLogic: {
      closingOption: 'buyer-designates',
      sellerDesignatesClosingAgent: false,
      buyerDesignatesClosingAgent: true,
    },
    autoGeneratedFields: {
      countyTaxId: 'Miami-Dade County, Florida Property Tax ID: 02-3204-001-0010',
      multipleBuyerInitials: ['JS', 'JS', 'JS', 'JS', 'JS'],
      multipleSellerInitials: ['JD', 'JD', 'JD', 'JD', 'JD'],
      multipleDateFields: ['2024-06-01', '2024-06-01', '2024-06-01', '2024-06-01'],
      additionalTerms: [
        'Buyer to inspect property within 7 days.',
        'Seller to provide survey.',
        '', '', '', '', '', '', '', '', '', '', '', '', '', ''
      ],
    },
    additionalTerms: [
      'Buyer to inspect property within 7 days.',
      'Seller to provide survey.',
      '', '', '', '', '', '', '', '', '', '', '', '', '', ''
    ],
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);

    const validation = validateForm(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Use the new FloridaTemplate mapping system
      const { mappings: pdfData, errors: mappingErrors } = formatDataForNewAPI(formData);

      if (mappingErrors.length > 0) {
        console.warn('PDF mapping warnings:', mappingErrors);
        // Continue with generation but log warnings
      }

      console.log('Sending FloridaTemplate PDF data:', pdfData); // Debug log
      console.log(`Mapped ${Object.keys(pdfData).length} fields for FloridaTemplate.pdf`);

      // Use FloridaTemplate.pdf generation endpoint
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      const apiUrl = isLocalhost
        ? 'http://localhost:3001/generate-pdf'  // Use the FloridaTemplate server
        : `${window.location.origin}/.netlify/functions/generate-pdf`;

      console.log('Using FloridaTemplate API URL:', apiUrl);
      console.log('Is localhost:', isLocalhost);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(pdfData),
      });
      
      console.log('Response status:', response.status); // Debug log
      
      if (!response.ok) throw new Error('PDF generation failed');
      
      const blob = await response.blob();
      console.log('Received blob:', blob.size, 'bytes'); // Debug log
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'Florida-Purchase-Agreement-Filled.pdf';  // Updated filename for FloridaTemplate
      document.body.appendChild(a); // Ensure the element is in the DOM
      a.click();
      document.body.removeChild(a); // Clean up
      window.URL.revokeObjectURL(url);
      
      setSubmitSuccess(true);
      setTimeout(() => setSubmitSuccess(false), 3000);
    } catch (error) {
      console.error('PDF generation error:', error);
      setErrors(['An error occurred while generating the PDF. Please try again.']);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTestMapping = async () => {
    try {
      // Test the FloridaTemplate mapping
      const { mappings, errors } = formatDataForNewAPI(formData);

      const testResults = {
        totalFields: Object.keys(mappings).length,
        mappingErrors: errors.length,
        sampleMappings: Object.entries(mappings).slice(0, 5),
        hasRequiredFields: mappings["'(Text_1)'"] && mappings["'(Text_2)'"] && mappings["'(Number_1)'"]
      };

      // Show test results
      if (testResults.hasRequiredFields && testResults.mappingErrors === 0) {
        alert(`✅ FloridaTemplate Mapping Test Successful!\n\nTotal Fields Mapped: ${testResults.totalFields}\nMapping Errors: ${testResults.mappingErrors}\nReady to generate your Florida agreement!`);
      } else {
        alert(`⚠️ FloridaTemplate Mapping Issues:\n\nTotal Fields: ${testResults.totalFields}\nErrors: ${testResults.mappingErrors}\nRequired Fields Present: ${testResults.hasRequiredFields}`);
      }

    } catch (error) {
      console.error('FloridaTemplate mapping test error:', error);
      setErrors(['Failed to test FloridaTemplate mapping. Please try again.']);
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else if (currentStep === 0) {
      setCurrentStep(-1); // Go back to landing page
    }
  };

  // Landing Page
  if (currentStep === -1) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl mb-8 shadow-2xl">
              <FileText className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Florida Purchase Agreement
              <span className="block text-3xl text-blue-600 font-semibold mt-2">Form Filler</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Generate your residential purchase agreement in minutes. Simply enter the property address to get started with our guided form process.
            </p>
          </div>

          {/* Address Input Section */}
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
              <div className="p-8">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-2xl mb-4">
                    <MapPin className="h-6 w-6 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Enter Property Address</h2>
                  <p className="text-gray-600">Start by entering the address of the property you're purchasing</p>
                </div>

                <div className="relative">
                  <div className="relative">
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      {isLoadingAutocomplete ? (
                        <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                      ) : (
                        <Search className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <input
                      ref={inputRef}
                      type="text"
                      value={addressInput}
                      onChange={(e) => setAddressInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleStartForm()}
                      className="w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white shadow-sm disabled:opacity-50"
                      placeholder="123 Main Street, Miami, FL 33101"
                      disabled={isLoadingPropertyDetail}
                    />
                  </div>

                  {/* Address Suggestions Dropdown rendered via Portal */}
                  {showSuggestions && addressSuggestions.length > 0 && typeof window !== 'undefined' && ReactDOM.createPortal(
                    <div
                      className="z-[9999] bg-white border border-blue-300 rounded-2xl shadow-2xl p-2 overflow-y-auto max-h-96 custom-scrollbar"
                      style={{
                        position: 'absolute',
                        top: dropdownStyle.top,
                        left: dropdownStyle.left,
                        width: dropdownStyle.width,
                      }}
                    >
                      <div className="px-4 py-3 bg-blue-50 border-b border-blue-200 rounded-t-2xl">
                        <div className="flex items-center gap-2 text-blue-700 text-sm font-medium">
                          <MapPin className="h-4 w-4" />
                          <span>Select an address ({addressSuggestions.length} found)</span>
                        </div>
                      </div>
                      {addressSuggestions.map((address, index) => (
                        <button
                          key={index}
                          onClick={() => handleAddressSelect(address)}
                          className="w-full px-5 py-4 text-left transition-all duration-200 border-b border-gray-100 flex items-center gap-3 disabled:opacity-50 group focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white hover:bg-blue-50 active:bg-blue-100 last:rounded-b-2xl"
                          disabled={isLoadingPropertyDetail}
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-gray-100 group-hover:bg-blue-100 rounded-lg flex items-center justify-center transition-colors">
                            <MapPin className="h-4 w-4 text-gray-500 group-hover:text-blue-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-gray-900 font-medium truncate">{address}</div>
                            <div className="text-xs text-gray-500 mt-1">Click to select this address</div>
                          </div>
                          <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                            <ArrowRight className="h-4 w-4 text-blue-500" />
                          </div>
                        </button>
                      ))}
                    </div>,
                    document.body
                  )}
                </div>

                {/* API Error Message */}
                {apiError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-xl">
                    <div className="flex items-center gap-2 text-red-700 text-sm">
                      <AlertCircle className="h-4 w-4" />
                      <span>{apiError}</span>
                    </div>
                  </div>
                )}

                {/* Loading Property Details */}
                {isLoadingPropertyDetail && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                    <div className="flex items-center gap-3 text-blue-700">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span className="text-sm font-medium">Loading property details...</span>
                    </div>
                  </div>
                )}

                <button
                  onClick={handleStartForm}
                  disabled={!addressInput.trim() || isLoadingPropertyDetail}
                  className="w-full mt-6 flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-2xl hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {isLoadingPropertyDetail ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin" />
                      Loading Details...
                    </>
                  ) : (
                    <>
                      Start Agreement Form
                      <ArrowRight className="h-5 w-5" />
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Features Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">State Compliant</h3>
                <p className="text-sm text-gray-600">Florida "As-Is" residential purchase agreement format</p>
              </div>
              
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Auto-Fill Fields</h3>
                <p className="text-sm text-gray-600">Smart form that populates multiple PDF fields automatically</p>
              </div>
              
              <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Download className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Instant PDF</h3>
                <p className="text-sm text-gray-600">Generate your completed agreement in seconds</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-2xl mb-6 shadow-lg">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Florida Purchase Agreement
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Complete your residential purchase agreement in minutes with our guided form
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600">
              Step {currentStep + 1} of {steps.length}
            </span>
            <span className="text-sm font-medium text-gray-600">
              {Math.round(((currentStep + 1) / steps.length) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Step Navigation */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {steps.map((step, index) => (
            <button
              key={step.id}
              onClick={() => setCurrentStep(index)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                index === currentStep
                  ? 'bg-blue-600 text-white shadow-lg'
                  : index < currentStep
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {step.title}
            </button>
          ))}
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Current Step Content */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
              <h2 className="text-2xl font-bold text-white">
                {steps[currentStep].title}
              </h2>
            </div>
            
            <div className="p-8">
              {currentStep === 0 && (
                <PropertyInfo
                  data={formData.propertyInfo}
                  onChange={(data) => setFormData({ ...formData, propertyInfo: data })}
                />
              )}
              {currentStep === 1 && (
                <PropertyDetails
                  data={formData.propertyInfo}
                  onChange={(data) => setFormData({ ...formData, propertyInfo: data })}
                />
              )}
              {currentStep === 2 && (
                <FinancialBasic
                  data={formData.financialInfo}
                  onChange={(data) => setFormData({ ...formData, financialInfo: data })}
                />
              )}
              {currentStep === 3 && (
                <FinancialDates
                  data={formData.financialInfo}
                  onChange={(data) => setFormData({ ...formData, financialInfo: data })}
                />
              )}
              {currentStep === 4 && (
                <PaymentMethod
                  data={formData.paymentMethod}
                  onChange={(data) => setFormData({ ...formData, paymentMethod: data })}
                  purchasePrice={formData.financialInfo.purchasePrice}
                  initialDeposit={formData.financialInfo.initialDeposit}
                />
              )}
              {currentStep === 5 && (
                <EscrowInfo
                  data={formData.escrowInfo}
                  onChange={(data) => setFormData({ ...formData, escrowInfo: data })}
                />
              )}
              {currentStep === 6 && (
                <PartyDetails
                  data={formData.partyDetails}
                  onChange={(data) => setFormData({ ...formData, partyDetails: data })}
                />
              )}
              {currentStep === 7 && (
                <TitleClosingLogic
                  data={formData.titleClosingLogic}
                  onChange={(data) => setFormData({ ...formData, titleClosingLogic: data })}
                />
              )}
              {currentStep === 8 && (
                <AdditionalTerms
                  data={formData.additionalTerms}
                  onChange={(data) => setFormData({ ...formData, additionalTerms: data })}
                />
              )}
              {currentStep === 9 && (
                <div className="space-y-8">
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                      <Check className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800">Review & Generate Agreement</h3>
                    <p className="text-gray-600">Review auto-generated fields and generate your purchase agreement</p>
                  </div>

                  {/* PDF Mapping Debug */}
                  <PDFMappingDebug formData={formData} />
                </div>
              )}
            </div>
          </div>

          {/* Auto-Generated Fields Preview */}
          {currentStep === 9 && (
            <AutoGeneratedFields
              data={formData.autoGeneratedFields}
              county={formData.propertyInfo.county}
              buyerInitials={formData.partyDetails.buyerInitials}
              sellerInitials={formData.partyDetails.sellerInitials}
              contractDate={formData.partyDetails.contractDate}
              additionalTerms={formData.additionalTerms}
            />
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center pt-8">
            <button
              type="button"
              onClick={prevStep}
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-all duration-200"
            >
              {currentStep === 0 ? 'Back to Address' : currentStep === 9 ? 'Back to Terms' : 'Previous'}
            </button>
            <button
              type="button"
              onClick={() => setFormData(mockFormData)}
              className="ml-4 px-6 py-3 bg-yellow-100 text-yellow-800 rounded-xl font-medium hover:bg-yellow-200 transition-all duration-200"
            >
              Fill with Mock Data
            </button>
            {currentStep < 9 ? (
              <button
                type="button"
                onClick={nextStep}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl ${
                  currentStep === 8 
                    ? 'bg-green-600 text-white hover:bg-green-700' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {currentStep === 8 ? 'Review & Generate' : 'Next Step'}
                <ArrowRight className="h-4 w-4" />
              </button>
            ) : currentStep === 9 ? (
              <div className="flex gap-4">
                {/* Test FloridaTemplate Mapping Button */}
                <button
                  type="button"
                  onClick={handleTestMapping}
                  disabled={isSubmitting}
                  className="flex items-center gap-3 px-6 py-4 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Eye className="h-5 w-5" />
                  Test Mapping
                </button>

                {/* Generate PDF Button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-green-800 focus:ring-4 focus:ring-green-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl text-lg"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Generating PDF...
                    </>
                  ) : submitSuccess ? (
                    <>
                      <Check className="h-5 w-5" />
                      PDF Generated!
                    </>
                  ) : (
                    <>
                      <Download className="h-5 w-5" />
                      Generate Agreement
                    </>
                  )}
                </button>
              </div>
            ) : null}
          </div>

          {/* Error Messages */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="flex items-center gap-3 text-red-800 mb-3">
                <AlertCircle className="h-5 w-5" />
                <span className="font-semibold">Please fix the following errors:</span>
              </div>
              <ul className="list-disc list-inside text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </div>
          )}
        </form>

        {/* Footer */}
        <div className="mt-16 text-center text-sm text-gray-500 bg-white rounded-xl p-6 shadow-sm">
          <p className="leading-relaxed">
            This form generates a Florida state-compliant "As-Is" Residential Purchase Agreement.
            <br />
            Please consult with a licensed real estate attorney before finalizing any legal documents.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FormWizard; 