import React from 'react';
import { Zap, Copy, Calendar, FileText, CheckCircle } from 'lucide-react';
import { AutoGeneratedFields as AutoGeneratedFieldsType } from '../types/FormTypes';

// Checkbox options from field_mapping.json
const inspectionOptions = [
  { name: 'as_is_condition', label: 'As-Is Condition' },
  { name: 'seller_repairs', label: 'Seller Repairs' },
  { name: 'buyer_inspection', label: 'Buyer Inspection' },
  { name: 'professional_inspection', label: 'Professional Inspection' },
  { name: 'termite_inspection', label: 'Termite Inspection' },
  { name: 'roof_inspection', label: 'Roof Inspection' },
  { name: 'hvac_inspection', label: 'HVAC Inspection' },
  { name: 'electrical_inspection', label: 'Electrical Inspection' },
  { name: 'plumbing_inspection', label: 'Plumbing Inspection' },
  { name: 'pool_inspection', label: 'Pool Inspection' },
  { name: 'dock_inspection', label: 'Dock Inspection' },
  { name: 'environmental_inspection', label: 'Environmental Inspection' },
  { name: 'lead_paint_inspection', label: 'Lead Paint Inspection' },
  { name: 'radon_inspection', label: 'Radon Inspection' },
  { name: 'mold_inspection', label: 'Mold Inspection' },
  { name: 'structural_inspection', label: 'Structural Inspection' },
  { name: 'foundation_inspection', label: 'Foundation Inspection' },
  { name: 'septic_inspection', label: 'Septic Inspection' },
  { name: 'well_inspection', label: 'Well Inspection' },
  { name: 'survey_required', label: 'Survey Required' },
  { name: 'appraisal_required', label: 'Appraisal Required' },
];

const applianceOptions = [
  { name: 'refrigerator', label: 'Refrigerator' },
  { name: 'washer', label: 'Washer' },
  { name: 'dryer', label: 'Dryer' },
  { name: 'dishwasher', label: 'Dishwasher' },
  { name: 'microwave', label: 'Microwave' },
  { name: 'oven_range', label: 'Oven/Range' },
  { name: 'garbage_disposal', label: 'Garbage Disposal' },
  { name: 'wine_cooler', label: 'Wine Cooler' },
  { name: 'ice_maker', label: 'Ice Maker' },
  { name: 'security_system', label: 'Security System' },
  { name: 'garage_door_openers', label: 'Garage Door Openers' },
  { name: 'pool_equipment', label: 'Pool Equipment' },
  { name: 'spa_equipment', label: 'Spa Equipment' },
  { name: 'dock_equipment', label: 'Dock Equipment' },
  { name: 'outdoor_kitchen', label: 'Outdoor Kitchen' },
  { name: 'generator', label: 'Generator' },
  { name: 'solar_panels', label: 'Solar Panels' },
  { name: 'water_softener', label: 'Water Softener' },
  { name: 'reverse_osmosis', label: 'Reverse Osmosis' },
  { name: 'central_vacuum', label: 'Central Vacuum' },
  { name: 'intercom_system', label: 'Intercom System' },
  { name: 'sound_system', label: 'Sound System' },
  { name: 'lighting_controls', label: 'Lighting Controls' },
  { name: 'irrigation_system', label: 'Irrigation System' },
  { name: 'landscape_lighting', label: 'Landscape Lighting' },
  { name: 'outdoor_speakers', label: 'Outdoor Speakers' },
  { name: 'fire_pit', label: 'Fire Pit' },
  { name: 'pergola', label: 'Pergola' },
  { name: 'gazebo', label: 'Gazebo' },
];

const closingOptions = [
  { name: 'title_insurance', label: 'Title Insurance' },
  { name: 'survey_required_closing', label: 'Survey Required (Closing)' },
];

interface AutoGeneratedFieldsProps {
  data: AutoGeneratedFieldsType;
  county: string;
  buyerInitials: string;
  sellerInitials: string;
  contractDate: string;
  additionalTerms: string[];
}

const AutoGeneratedFields: React.FC<AutoGeneratedFieldsProps> = ({ 
  data, 
  county, 
  buyerInitials, 
  sellerInitials, 
  contractDate, 
  additionalTerms 
}) => {
  const generateCountyTaxId = (county: string): string => {
    if (!county) return '';
    return `${county.toUpperCase()}, Florida Property Tax ID: [TAX ID NUMBER]`;
  };

  const populateBuyerInitials = (initials: string): string[] => {
    return initials ? Array(5).fill(initials) : [];
  };

  const populateSellerInitials = (initials: string): string[] => {
    return initials ? Array(5).fill(initials) : [];
  };

  const populateDateFields = (date: string): string[] => {
    return date ? Array(4).fill(date) : [];
  };

  const splitAdditionalTerms = (terms: string[]): string[] => {
    return terms.filter(term => term.trim()).map((term, index) => 
      `[ADDITIONAL TERMS ${index + 1}]: ${term}`
    );
  };

  const autoFields = [
    {
      icon: FileText,
      title: 'County Tax ID',
      description: 'Auto-generates based on selected county',
      value: generateCountyTaxId(county),
      color: 'blue',
      isComplete: !!county
    },
    {
      icon: Copy,
      title: 'Buyer Initials',
      description: `Populates all 5 buyer initial fields`,
      value: buyerInitials ? `${buyerInitials} (×5 fields)` : '',
      color: 'green',
      isComplete: !!buyerInitials
    },
    {
      icon: Copy,
      title: 'Seller Initials',
      description: `Populates all 5 seller initial fields`,
      value: sellerInitials ? `${sellerInitials} (×5 fields)` : '',
      color: 'purple',
      isComplete: !!sellerInitials
    },
    {
      icon: Calendar,
      title: 'Date Fields',
      description: `Populates all 4 date fields`,
      value: contractDate ? `${contractDate} (×4 fields)` : '',
      color: 'red',
      isComplete: !!contractDate
    },
    {
      icon: FileText,
      title: 'Additional Terms',
      description: 'Split into 16 separate PDF fields',
      value: `${splitAdditionalTerms(additionalTerms).length} terms will be populated`,
      color: 'yellow',
      isComplete: additionalTerms.filter(term => term.trim()).length > 0
    }
  ];

  const colorClasses = {
    blue: { bg: 'bg-blue-50', border: 'border-blue-200', icon: 'text-blue-600', text: 'text-blue-800' },
    green: { bg: 'bg-green-50', border: 'border-green-200', icon: 'text-green-600', text: 'text-green-800' },
    purple: { bg: 'bg-purple-50', border: 'border-purple-200', icon: 'text-purple-600', text: 'text-purple-800' },
    red: { bg: 'bg-red-50', border: 'border-red-200', icon: 'text-red-600', text: 'text-red-800' },
    yellow: { bg: 'bg-yellow-50', border: 'border-yellow-200', icon: 'text-yellow-600', text: 'text-yellow-800' }
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
      <div className="bg-gradient-to-r from-yellow-500 to-orange-500 px-8 py-6">
        <div className="flex items-center gap-3">
          <Zap className="h-6 w-6 text-white" />
          <h2 className="text-2xl font-bold text-white">Auto-Generated Fields Preview</h2>
        </div>
        <p className="text-yellow-100 mt-2">
          These fields will be automatically populated in your PDF document
        </p>
      </div>

      <div className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {autoFields.map((field, index) => {
            const colors = colorClasses[field.color as keyof typeof colorClasses];
            const IconComponent = field.icon;
            
            return (
              <div
                key={index}
                className={`${colors.bg} ${colors.border} border rounded-xl p-6 transition-all duration-200 hover:shadow-md`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 bg-white rounded-lg shadow-sm`}>
                      <IconComponent className={`h-5 w-5 ${colors.icon}`} />
                    </div>
                    <div>
                      <h3 className={`font-semibold ${colors.text}`}>{field.title}</h3>
                      <p className="text-sm text-gray-600">{field.description}</p>
                    </div>
                  </div>
                  {field.isComplete && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>
                
                <div className="bg-white rounded-lg p-3 border">
                  {field.value ? (
                    <div className="text-sm font-medium text-gray-800">{field.value}</div>
                  ) : (
                    <div className="text-sm text-gray-400 italic">Will be populated when data is entered</div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-8 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 border border-blue-200">
          <h4 className="font-semibold text-gray-800 mb-3">How Auto-Generation Works</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Data entered once → Multiple PDF fields populated</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Initials appear wherever signatures are needed</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Dates populate all contract date fields</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>Terms split into individual PDF sections</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoGeneratedFields;

export const CheckboxGroups: React.FC<{ data: any, onChange: (data: any) => void }> = ({ data, onChange }) => (
  <div className="space-y-8">
    <div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">Inspections</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {inspectionOptions.map(opt => (
          <label key={opt.name} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={!!data.inspections?.[opt.name]}
              onChange={e => onChange({
                ...data,
                inspections: { ...data.inspections, [opt.name]: e.target.checked }
              })}
            />
            {opt.label}
          </label>
        ))}
      </div>
    </div>
    <div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">Appliances & Features</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {applianceOptions.map(opt => (
          <label key={opt.name} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={!!data.appliances?.[opt.name]}
              onChange={e => onChange({
                ...data,
                appliances: { ...data.appliances, [opt.name]: e.target.checked }
              })}
            />
            {opt.label}
          </label>
        ))}
      </div>
    </div>
    <div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">Closing</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {closingOptions.map(opt => (
          <label key={opt.name} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={!!data.closing?.[opt.name]}
              onChange={e => onChange({
                ...data,
                closing: { ...data.closing, [opt.name]: e.target.checked }
              })}
            />
            {opt.label}
          </label>
        ))}
      </div>
    </div>
  </div>
);