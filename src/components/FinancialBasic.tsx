import React from 'react';
import { DollarSign, Calculator } from 'lucide-react';
import { FinancialInfo as FinancialInfoType } from '../types/FormTypes';

interface FinancialBasicProps {
  data: FinancialInfoType;
  onChange: (data: FinancialInfoType) => void;
}

const FinancialBasic: React.FC<FinancialBasicProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof FinancialInfoType, value: string | number) => {
    onChange({ ...data, [field]: value });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const parseCurrency = (value: string): number => {
    return parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
  };

  const calculateBalance = () => {
    return Math.max(0, data.purchasePrice - data.initialDeposit);
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
          <DollarSign className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Purchase Price & Deposit</h3>
        <p className="text-gray-600">Enter the financial terms for this purchase</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <DollarSign className="h-4 w-4 text-green-600" />
            Purchase Price
          </label>
          <div className="relative">
            <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
            <input
              type="text"
              value={data.purchasePrice ? formatCurrency(data.purchasePrice) : ''}
              onChange={(e) => handleChange('purchasePrice', parseCurrency(e.target.value))}
              className="w-full pl-14 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-2xl font-bold text-center"
              placeholder="$0"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <DollarSign className="h-4 w-4 text-blue-600" />
            Initial Deposit (Earnest Money)
          </label>
          <div className="relative">
            <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
            <input
              type="text"
              value={data.initialDeposit ? formatCurrency(data.initialDeposit) : ''}
              onChange={(e) => handleChange('initialDeposit', parseCurrency(e.target.value))}
              className="w-full pl-14 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-2xl font-bold text-center"
              placeholder="$0"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Calculator className="h-4 w-4 text-purple-600" />
            Balance to Close
          </label>
          <div className="relative">
            <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
            <input
              type="text"
              value={data.balanceToClose ? formatCurrency(data.balanceToClose) : formatCurrency(calculateBalance())}
              onChange={(e) => handleChange('balanceToClose', parseCurrency(e.target.value))}
              className="w-full pl-14 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-2xl font-bold text-center"
              placeholder="$0"
              required
            />
          </div>
          <p className="text-sm text-gray-500 text-center">
            Auto-calculated: {formatCurrency(calculateBalance())} (Purchase Price - Initial Deposit)
          </p>
        </div>
      </div>

      {/* Additional Financial Details */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Additional Deposit</label>
            <input
              type="text"
              value={data.additionalDeposit || ''}
              onChange={(e) => handleChange('additionalDeposit', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Loan Amount</label>
            <input
              type="text"
              value={data.loanAmount || ''}
              onChange={(e) => handleChange('loanAmount', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Down Payment</label>
            <input
              type="text"
              value={data.downPayment || ''}
              onChange={(e) => handleChange('downPayment', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Closing Costs</label>
            <input
              type="text"
              value={data.closingCosts || ''}
              onChange={(e) => handleChange('closingCosts', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Inspection Fee</label>
            <input
              type="text"
              value={data.inspectionFee || ''}
              onChange={(e) => handleChange('inspectionFee', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Appraisal Fee</label>
            <input
              type="text"
              value={data.appraisalFee || ''}
              onChange={(e) => handleChange('appraisalFee', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Survey Cost</label>
            <input
              type="text"
              value={data.surveyCost || ''}
              onChange={(e) => handleChange('surveyCost', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Special Assessments</label>
            <input
              type="text"
              value={data.specialAssessments || ''}
              onChange={(e) => handleChange('specialAssessments', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">HOA Fees</label>
            <input
              type="text"
              value={data.hoaFees || ''}
              onChange={(e) => handleChange('hoaFees', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Property Taxes</label>
            <input
              type="text"
              value={data.propertyTaxes || ''}
              onChange={(e) => handleChange('propertyTaxes', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Insurance Cost</label>
            <input
              type="text"
              value={data.insuranceCost || ''}
              onChange={(e) => handleChange('insuranceCost', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Utility Costs</label>
            <input
              type="text"
              value={data.utilityCosts || ''}
              onChange={(e) => handleChange('utilityCosts', parseCurrency(e.target.value))}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="$0"
            />
          </div>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Maintenance Costs</label>
          <input
            type="text"
            value={data.maintenanceCosts || ''}
            onChange={(e) => handleChange('maintenanceCosts', parseCurrency(e.target.value))}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="$0"
          />
        </div>
      </div>

      {/* Financial Summary */}
      {(data.purchasePrice > 0 || data.initialDeposit > 0) && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <Calculator className="h-5 w-5 text-green-600" />
            Financial Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 shadow-sm text-center">
              <div className="text-2xl font-bold text-green-600">{formatCurrency(data.purchasePrice)}</div>
              <div className="text-sm text-gray-600">Purchase Price</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm text-center">
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(data.initialDeposit)}</div>
              <div className="text-sm text-gray-600">Initial Deposit</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm text-center">
              <div className="text-2xl font-bold text-purple-600">{formatCurrency(data.balanceToClose || calculateBalance())}</div>
              <div className="text-sm text-gray-600">Balance to Close</div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <h4 className="font-semibold text-green-900 mb-2">About These Amounts</h4>
        <div className="text-sm text-green-800 space-y-1">
          <p>• <strong>Purchase Price:</strong> The total amount you're paying for the property</p>
          <p>• <strong>Initial Deposit:</strong> Earnest money to show good faith (typically 1-3% of purchase price)</p>
          <p>• <strong>Balance to Close:</strong> Remaining amount due at closing (may include financing)</p>
        </div>
      </div>
    </div>
  );
};

export default FinancialBasic;