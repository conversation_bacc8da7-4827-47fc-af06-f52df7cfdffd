import React from 'react';
import { Shield, Building, MapPin } from 'lucide-react';
import { EscrowInfo as EscrowInfoType } from '../types/FormTypes';

interface EscrowInfoProps {
  data: EscrowInfoType;
  onChange: (data: EscrowInfoType) => void;
}

const EscrowInfo: React.FC<EscrowInfoProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof EscrowInfoType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-full mb-4">
          <Shield className="h-6 w-6 text-indigo-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Escrow & Title Information</h3>
        <p className="text-gray-600">Specify the title company that will handle the closing</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Building className="h-4 w-4 text-indigo-600" />
            Title Company / Escrow Agent Name
          </label>
          <input
            type="text"
            value={data.escrowAgentName}
            onChange={(e) => handleChange('escrowAgentName', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="ABC Title Company"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <MapPin className="h-4 w-4 text-red-600" />
            Title Company Address
          </label>
          <textarea
            value={data.escrowAgentAddress}
            onChange={(e) => handleChange('escrowAgentAddress', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
            placeholder="123 Business Blvd, Suite 100&#10;Miami, FL 33101"
            required
          />
        </div>

        {/* Escrow Agent Email and Fax */}
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            Escrow Agent Email
          </label>
          <input
            type="email"
            value={data.escrowAgentEmail || ''}
            onChange={(e) => handleChange('escrowAgentEmail', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="<EMAIL>"
          />
        </div>
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            Escrow Agent Fax
          </label>
          <input
            type="tel"
            value={data.escrowAgentFax || ''}
            onChange={(e) => handleChange('escrowAgentFax', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="(*************"
          />
        </div>
      </div>

      <div className="bg-indigo-50 rounded-xl p-6 border border-indigo-200">
        <h4 className="font-semibold text-indigo-900 mb-2">Important Note</h4>
        <p className="text-sm text-indigo-800">
          The title company will handle the escrow of funds, title search, and closing process. 
          Make sure to choose a reputable, licensed title company in Florida.
        </p>
      </div>
    </div>
  );
};

export default EscrowInfo;