import React from 'react';
import { FileText, Home, Package } from 'lucide-react';
import { PropertyInfo as PropertyInfoType } from '../types/FormTypes';

interface PropertyDetailsProps {
  data: PropertyInfoType;
  onChange: (data: PropertyInfoType) => void;
}

const PropertyDetails: React.FC<PropertyDetailsProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof PropertyInfoType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
          <FileText className="h-6 w-6 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Property Details</h3>
        <p className="text-gray-600">Legal description and included/excluded items</p>
      </div>

      {/* Legal Description */}
      <div className="space-y-4">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <FileText className="h-4 w-4 text-purple-600" />
          Legal Description
        </label>
        <textarea
          value={data.legalDescription1}
          onChange={(e) => handleChange('legalDescription1', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Enter the legal description of the property"
          required
        />
        <textarea
          value={data.legalDescription2}
          onChange={(e) => handleChange('legalDescription2', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Additional legal description (optional)"
        />
      </div>

      {/* Property Inclusions/Exclusions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Package className="h-4 w-4 text-green-600" />
            Personal Property Included
          </label>
          <textarea
            value={data.personalPropertyIncluded}
            onChange={(e) => handleChange('personalPropertyIncluded', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
            placeholder="List included personal property (appliances, fixtures, etc.)"
          />
        </div>

        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Package className="h-4 w-4 text-red-600" />
            Items Excluded
          </label>
          <textarea
            value={data.itemsExcluded}
            onChange={(e) => handleChange('itemsExcluded', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
            placeholder="List excluded items"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          <Home className="h-4 w-4 text-blue-600" />
          Additional Personal Property
        </label>
        <textarea
          value={data.additionalPersonalProperty}
          onChange={(e) => handleChange('additionalPersonalProperty', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Additional personal property details"
        />
      </div>

      {/* More Property Details */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          Fixtures Included
        </label>
        <textarea
          value={data.fixturesIncluded || ''}
          onChange={(e) => handleChange('fixturesIncluded', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="List fixtures included (e.g., ceiling fans, built-ins)"
        />
      </div>
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          Other Items
        </label>
        <textarea
          value={data.otherItems || ''}
          onChange={(e) => handleChange('otherItems', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Other items to be included or noted"
        />
      </div>
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
          Additional Property Details
        </label>
        <textarea
          value={data.additionalPropertyDetails || ''}
          onChange={(e) => handleChange('additionalPropertyDetails', e.target.value)}
          rows={2}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
          placeholder="Any additional property details"
        />
      </div>

      {/* Utilities & Maintenance Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Electric Utility</label>
          <input
            type="text"
            value={data.utilitiesElectric || ''}
            onChange={(e) => handleChange('utilitiesElectric', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Electric company/provider"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Gas Utility</label>
          <input
            type="text"
            value={data.utilitiesGas || ''}
            onChange={(e) => handleChange('utilitiesGas', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Gas company/provider"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Water Utility</label>
          <input
            type="text"
            value={data.utilitiesWater || ''}
            onChange={(e) => handleChange('utilitiesWater', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Water company/provider"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Cable Utility</label>
          <input
            type="text"
            value={data.utilitiesCable || ''}
            onChange={(e) => handleChange('utilitiesCable', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Cable company/provider"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Internet Utility</label>
          <input
            type="text"
            value={data.utilitiesInternet || ''}
            onChange={(e) => handleChange('utilitiesInternet', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Internet company/provider"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Trash Utility</label>
          <input
            type="text"
            value={data.utilitiesTrash || ''}
            onChange={(e) => handleChange('utilitiesTrash', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Trash company/provider"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Security Utility</label>
          <input
            type="text"
            value={data.utilitiesSecurity || ''}
            onChange={(e) => handleChange('utilitiesSecurity', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Security company/provider"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Pool Maintenance</label>
          <input
            type="text"
            value={data.maintenancePool || ''}
            onChange={(e) => handleChange('maintenancePool', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Pool maintenance company/provider"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Landscape Maintenance</label>
          <input
            type="text"
            value={data.maintenanceLandscape || ''}
            onChange={(e) => handleChange('maintenanceLandscape', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Landscape maintenance company/provider"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">HVAC Maintenance</label>
          <input
            type="text"
            value={data.maintenanceHvac || ''}
            onChange={(e) => handleChange('maintenanceHvac', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="HVAC maintenance company/provider"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">Dock Maintenance</label>
          <input
            type="text"
            value={data.maintenanceDock || ''}
            onChange={(e) => handleChange('maintenanceDock', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            placeholder="Dock maintenance company/provider"
          />
        </div>
      </div>

      <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
        <h4 className="font-semibold text-purple-900 mb-2">Property Details Guide</h4>
        <div className="text-sm text-purple-800 space-y-1">
          <p>• <strong>Legal Description:</strong> The official property description from the deed</p>
          <p>• <strong>Included Items:</strong> Appliances, fixtures, and personal property that stay with the home</p>
          <p>• <strong>Excluded Items:</strong> Personal belongings that the seller will take</p>
        </div>
      </div>

      {/* Features, Restrictions, Improvements Section */}
      <div className="space-y-2 mt-8">
        <label className="text-sm font-semibold text-gray-700">Rental Restrictions</label>
        <input
          type="text"
          value={data.rentalRestrictions || ''}
          onChange={(e) => handleChange('rentalRestrictions', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Describe any rental restrictions"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Pet Restrictions</label>
        <input
          type="text"
          value={data.petRestrictions || ''}
          onChange={(e) => handleChange('petRestrictions', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Describe any pet restrictions"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Parking Spaces</label>
        <input
          type="text"
          value={data.parkingSpaces || ''}
          onChange={(e) => handleChange('parkingSpaces', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Number and type of parking spaces"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Storage Areas</label>
        <input
          type="text"
          value={data.storageAreas || ''}
          onChange={(e) => handleChange('storageAreas', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Describe any storage areas"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Outdoor Features</label>
        <input
          type="text"
          value={data.outdoorFeatures || ''}
          onChange={(e) => handleChange('outdoorFeatures', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Describe outdoor features (pool, patio, etc.)"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Recent Improvements</label>
        <input
          type="text"
          value={data.recentImprovements || ''}
          onChange={(e) => handleChange('recentImprovements', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Describe any recent improvements"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Warranty Information</label>
        <input
          type="text"
          value={data.warrantyInformation || ''}
          onChange={(e) => handleChange('warrantyInformation', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Warranty details (if any)"
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-semibold text-gray-700">Disclosure Items</label>
        <input
          type="text"
          value={data.disclosureItems || ''}
          onChange={(e) => handleChange('disclosureItems', e.target.value)}
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
          placeholder="Disclosures (lead paint, etc.)"
        />
      </div>
    </div>
  );
};

export default PropertyDetails;