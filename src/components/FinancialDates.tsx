import React from 'react';
import { Calendar, Clock, AlertTriangle } from 'lucide-react';
import { FinancialInfo as FinancialInfoType } from '../types/FormTypes';

interface FinancialDatesProps {
  data: FinancialInfoType;
  onChange: (data: FinancialInfoType) => void;
}

const FinancialDates: React.FC<FinancialDatesProps> = ({ data, onChange }) => {
  const handleChange = (field: keyof FinancialInfoType, value: string) => {
    onChange({ ...data, [field]: value });
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysFromNow = (dateString: string) => {
    if (!dateString) return null;
    const targetDate = new Date(dateString);
    const today = new Date();
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getMinDate = () => {
    const today = new Date();
    today.setDate(today.getDate() + 1); // Minimum tomorrow
    return today.toISOString().split('T')[0];
  };

  const getRecommendedDate = () => {
    const today = new Date();
    today.setDate(today.getDate() + 30); // 30 days from now
    return today.toISOString().split('T')[0];
  };

  const daysUntilClosing = getDaysFromNow(data.closingDate);

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
          <Calendar className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Timeline & Important Dates</h3>
        <p className="text-gray-600">Set the closing date and timeline for your purchase</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
            <Calendar className="h-4 w-4 text-blue-600" />
            Closing Date
          </label>
          <input
            type="date"
            value={data.closingDate}
            onChange={(e) => handleChange('closingDate', e.target.value)}
            min={getMinDate()}
            className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-lg font-semibold"
            required
          />
          {data.closingDate && (
            <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-blue-800 font-medium">{formatDate(data.closingDate)}</p>
              {daysUntilClosing !== null && (
                <p className="text-blue-600 text-sm mt-1">
                  {daysUntilClosing > 0 
                    ? `${daysUntilClosing} days from today`
                    : daysUntilClosing === 0 
                    ? 'Today'
                    : `${Math.abs(daysUntilClosing)} days ago`
                  }
                </p>
              )}
            </div>
          )}
        </div>

        {/* Quick Date Options */}
        <div className="space-y-3">
          <label className="text-sm font-semibold text-gray-700">Quick Options</label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[15, 30, 45, 60].map((days) => {
              const futureDate = new Date();
              futureDate.setDate(futureDate.getDate() + days);
              const dateString = futureDate.toISOString().split('T')[0];
              
              return (
                <button
                  key={days}
                  type="button"
                  onClick={() => handleChange('closingDate', dateString)}
                  className={`p-3 rounded-xl border-2 transition-all duration-200 text-center ${
                    data.closingDate === dateString
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="font-semibold">{days} Days</div>
                  <div className="text-xs text-gray-600 mt-1">
                    {futureDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Additional Date/Deadline Fields */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Inspection Deadline</label>
            <input
              type="date"
              value={data.inspectionDeadline || ''}
              onChange={(e) => handleChange('inspectionDeadline', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Financing Deadline</label>
            <input
              type="date"
              value={data.financingDeadline || ''}
              onChange={(e) => handleChange('financingDeadline', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Appraisal Deadline</label>
            <input
              type="date"
              value={data.appraisalDeadline || ''}
              onChange={(e) => handleChange('appraisalDeadline', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Possession Date</label>
            <input
              type="date"
              value={data.possessionDate || ''}
              onChange={(e) => handleChange('possessionDate', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Final Walkthrough</label>
            <input
              type="date"
              value={data.finalWalkthrough || ''}
              onChange={(e) => handleChange('finalWalkthrough', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Document Delivery</label>
            <input
              type="date"
              value={data.documentDelivery || ''}
              onChange={(e) => handleChange('documentDelivery', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
            />
          </div>
        </div>
      </div>

      {/* Timeline Warnings */}
      {daysUntilClosing !== null && (
        <div className="space-y-4">
          {daysUntilClosing < 15 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <div>
                  <h4 className="font-semibold text-yellow-800">Short Timeline Notice</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    A {daysUntilClosing}-day closing timeline is quite fast. Ensure all parties can meet inspection, 
                    financing, and documentation deadlines.
                  </p>
                </div>
              </div>
            </div>
          )}

          {daysUntilClosing > 90 && (
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-semibold text-blue-800">Extended Timeline</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    A {daysUntilClosing}-day timeline provides ample time for all contingencies and requirements.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h4 className="font-semibold text-gray-800 mb-3">Typical Timeline Considerations</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span><strong>7-10 days:</strong> Home inspection period</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span><strong>21-30 days:</strong> Financing approval</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span><strong>3-5 days:</strong> Title search completion</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span><strong>2-3 days:</strong> Final walkthrough</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialDates;