import React, { useRef } from 'react';
import { Header } from '../landing-staging-main/components/layout/header';
import { Footer } from '../landing-staging-main/components/layout/footer';
import { AnimatedJourney } from '../landing-staging-main/components/hero/AnimatedJourney';
import { AnimatedSections } from '../landing-staging-main/components/AnimatedSections';
import FormWizard from './components/FormWizard';

const LandingPage: React.FC = () => {
  const formRef = useRef<HTMLDivElement>(null);

  // Handler to scroll/focus the form section
  const handleFocusForm = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      // Optionally, focus the first input in the form
      const input = formRef.current.querySelector('input, textarea, select') as HTMLElement;
      if (input) input.focus();
    }
  };

  return (
    <div className="bg-white text-slate-800 font-sans">
      <Header />
      {/* Animated Hero/Intro Section from AnimatedJourney */}
      <AnimatedJourney />

      {/* Animated Product Demo Sections */}
      <section className="py-12 bg-white border-b border-blue-100 animate-fade-in">
        <AnimatedSections onAddressInputFocus={handleFocusForm} />
      </section>

      {/* Product Demo Section (Live Form) */}
      <section id="demo" ref={formRef} className="py-20 bg-slate-50 border-b border-blue-100 animate-fade-in">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-blue-100 border border-blue-200 rounded-lg text-blue-700 text-sm font-semibold mb-6 animate-fade-in">
              <span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
              <span>LIVE PRODUCT DEMO</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">See How Fast You Can Generate a Contract</h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Fill out the form below to generate a compliant Florida purchase agreement PDF in minutes. No account required.
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <FormWizard />
          </div>
        </div>
      </section>

      {/* Pricing Section (reuse previous, but with animation classes) */}
      <section className="py-20 bg-white border-b border-blue-100 animate-fade-in-up">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center mb-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-blue-100 border border-blue-200 rounded-lg text-blue-700 text-sm font-semibold mb-8 animate-fade-in">
              <span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
              <span>PRICING</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6 font-serif">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Flexible plans for agents, teams, and brokerages. Start free, upgrade anytime.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Professional Plan */}
            <div className="bg-white rounded-lg border-2 border-slate-200 p-8 relative hover:shadow-xl transition-all hover:border-slate-300 animate-fade-in-up delay-100">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Professional</h3>
                <div className="text-4xl font-bold text-slate-800 mb-2">$49<span className="text-lg text-slate-600">/mo</span></div>
                <p className="text-slate-600">Perfect for individual agents</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>10 agreements/mo</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>All 50 states supported</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Property data integration</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Email support</li>
              </ul>
              <button className="w-full py-3 px-6 bg-blue-700 text-white font-semibold rounded-lg hover:bg-blue-800 transition-colors shadow-md">Start Free Trial</button>
            </div>
            {/* Business Plan */}
            <div className="bg-white rounded-lg border-2 border-blue-500 p-8 relative hover:shadow-xl transition-all transform scale-105 shadow-lg animate-fade-in-up delay-200">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-700 text-white px-4 py-1 rounded-full text-sm font-bold shadow-md">MOST POPULAR</span>
              </div>
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Business</h3>
                <div className="text-4xl font-bold text-blue-700 mb-2">$149<span className="text-lg text-slate-600">/mo</span></div>
                <p className="text-slate-600">For growing real estate teams</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>50 agreements/mo</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>All 50 states supported</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Advanced analytics</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Priority phone support</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Team collaboration tools</li>
              </ul>
              <button className="w-full py-3 px-6 bg-gradient-to-r from-blue-700 to-blue-500 text-white font-bold rounded-lg hover:from-blue-800 hover:to-blue-700 transition-all shadow-md">Start Free Trial</button>
            </div>
            {/* Enterprise Plan */}
            <div className="bg-white rounded-lg border-2 border-slate-200 p-8 relative hover:shadow-xl transition-all hover:border-slate-300 animate-fade-in-up delay-300">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">Enterprise</h3>
                <div className="text-4xl font-bold text-slate-800 mb-2">$399<span className="text-lg text-slate-600">/mo</span></div>
                <p className="text-slate-600">For large brokerages</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Unlimited agreements</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>White-label options</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>API access</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Dedicated account manager</li>
                <li className="flex items-center gap-3"><span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-bold">✓</span></span>Custom integrations</li>
              </ul>
              <button className="w-full py-3 px-6 bg-blue-700 text-white font-semibold rounded-lg hover:bg-blue-800 transition-colors shadow-md">Contact Sales</button>
            </div>
          </div>
          <div className="text-center mt-12 animate-fade-in-up delay-400">
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
              <span className="font-semibold">30-day money-back guarantee • No setup fees • Cancel anytime</span>
            </div>
          </div>
        </div>
      </section>

      {/* Trust/Social Proof Section (reuse previous, but with animation classes) */}
      <section className="py-16 bg-slate-50 border-b border-blue-100 animate-fade-in-up">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center mb-12">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-blue-100 border border-blue-200 rounded-lg text-blue-700 text-sm font-semibold mb-6 animate-fade-in">
              <span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
              <span>TRUSTED BY PROFESSIONALS</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4 font-serif">
              Built by Licensed Brokers, Trusted by Agents
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Our platform is developed and maintained by active real estate brokers with decades of experience in purchase agreements and legal compliance.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="text-center p-6 bg-white rounded-lg border border-slate-200 hover:shadow-lg transition-all animate-fade-in-up delay-100">
              <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🏆</span>
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Licensed Broker</h3>
              <p className="text-sm text-slate-600">Active real estate license</p>
            </div>
            <div className="text-center p-6 bg-white rounded-lg border border-slate-200 hover:shadow-lg transition-all animate-fade-in-up delay-200">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">⚖️</span>
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Legal Compliance</h3>
              <p className="text-sm text-slate-600">50-state requirements</p>
            </div>
            <div className="text-center p-6 bg-white rounded-lg border border-slate-200 hover:shadow-lg transition-all animate-fade-in-up delay-300">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🏢</span>
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Professional Grade</h3>
              <p className="text-sm text-slate-600">Industry standard forms</p>
            </div>
            <div className="text-center p-6 bg-white rounded-lg border border-slate-200 hover:shadow-lg transition-all animate-fade-in-up delay-400">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🔒</span>
              </div>
              <h3 className="font-bold text-slate-800 mb-2">Secure Platform</h3>
              <p className="text-sm text-slate-600">Enterprise security</p>
            </div>
          </div>
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 rounded-lg p-8 mt-8 animate-fade-in-up delay-500">
            <div className="grid md:grid-cols-3 gap-8 items-center">
              <div className="text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start gap-2 mb-4">
                  <div className="w-12 h-12 bg-blue-700 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl font-bold">👥</span>
                  </div>
                  <div>
                    <h3 className="font-bold text-slate-800 text-lg">Licensed Broker Team</h3>
                    <p className="text-sm text-blue-700">Active Florida Real Estate License</p>
                  </div>
                </div>
                <p className="text-slate-700">
                  Our development team includes active real estate brokers who understand the complexities of purchase agreements.
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-700 mb-2">15+</div>
                <p className="text-slate-700 font-semibold">Years Experience</p>
                <p className="text-sm text-slate-600">In real estate transactions</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-700 mb-2">50</div>
                <p className="text-slate-700 font-semibold">States Supported</p>
                <p className="text-sm text-slate-600">Compliant legal forms</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default LandingPage; 